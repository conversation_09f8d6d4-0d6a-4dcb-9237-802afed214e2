#!/usr/bin/env python3
"""
Test script to verify MoviePy installation and FFmpeg availability.

This script tests if MoviePy can be imported and if FFmpeg is properly configured.
"""

import sys
import subprocess
import tempfile
import os


def test_moviepy_import():
    """Test if MoviePy can be imported successfully."""
    print("🎬 Testing MoviePy Import...")
    try:
        from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
        print("✅ MoviePy imported successfully!")
        return True
    except ImportError as e:
        print(f"❌ MoviePy import failed: {e}")
        return False


def test_ffmpeg_availability():
    """Test if FFmpeg is available on the system."""
    print("\n🔧 Testing FFmpeg Availability...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg found: {version_line}")
            return True
        else:
            print(f"❌ FFmpeg command failed with return code: {result.returncode}")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in system PATH")
        return False
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg command timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking FFmpeg: {e}")
        return False


def test_moviepy_basic_functionality():
    """Test basic MoviePy functionality with a simple video creation."""
    print("\n🎥 Testing MoviePy Basic Functionality...")
    
    try:
        from moviepy.editor import ImageClip, concatenate_videoclips
        import numpy as np
        from PIL import Image
        
        # Create a temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a simple test image
            test_image_path = os.path.join(temp_dir, "test_image.png")
            
            # Create a simple 100x100 red image
            img_array = np.full((100, 100, 3), [255, 0, 0], dtype=np.uint8)
            img = Image.fromarray(img_array)
            img.save(test_image_path)
            
            print(f"  📸 Created test image: {test_image_path}")
            
            # Create a video clip from the image
            clip = ImageClip(test_image_path, duration=1.0)  # 1 second duration
            print("  🎬 Created ImageClip successfully")
            
            # Test video export
            test_video_path = os.path.join(temp_dir, "test_video.mp4")
            clip.write_videofile(
                test_video_path,
                fps=24,
                codec='libx264',
                verbose=False,
                logger=None
            )
            
            # Check if video file was created
            if os.path.exists(test_video_path):
                file_size = os.path.getsize(test_video_path)
                print(f"  ✅ Video created successfully: {test_video_path} ({file_size} bytes)")
                return True
            else:
                print("  ❌ Video file was not created")
                return False
                
    except Exception as e:
        print(f"  ❌ MoviePy functionality test failed: {e}")
        return False


def test_audio_support():
    """Test if MoviePy can handle audio processing."""
    print("\n🔊 Testing Audio Support...")
    
    try:
        from moviepy.editor import AudioFileClip
        print("  ✅ AudioFileClip imported successfully")
        
        # Test if we can create a silent audio clip
        from moviepy.audio.AudioClip import AudioClip
        
        def make_frame(t):
            return [0, 0]  # Silent stereo audio
        
        audio_clip = AudioClip(make_frame, duration=1.0, fps=44100)
        print("  ✅ Audio clip creation successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Audio support test failed: {e}")
        return False


def main():
    """Run all MoviePy tests."""
    print("🧪 MoviePy Installation Test Suite")
    print("=" * 50)
    
    tests = [
        ("MoviePy Import", test_moviepy_import),
        ("FFmpeg Availability", test_ffmpeg_availability),
        ("Basic Functionality", test_moviepy_basic_functionality),
        ("Audio Support", test_audio_support)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MoviePy is ready for video generation.")
        return 0
    else:
        print("⚠️  Some tests failed. Video generation may not work properly.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
