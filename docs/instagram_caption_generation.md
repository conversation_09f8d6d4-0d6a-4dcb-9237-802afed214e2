# Instagram Caption Generation

This document describes the Instagram caption generation feature that creates simple, shareable captions for Nepali learning content.

## Overview

The Instagram caption generation feature automatically creates social media-ready captions for task sets using the task title and predefined hashtags.

## API Endpoints

### Generate Complete Post (Recommended)

**POST** `/v1/management/sharepost/generate_post`

Generates both video and caption in a single call - the most convenient option!

### Create Instagram Caption Only

**POST** `/v1/management/sharepost/captions/create_caption`

Creates an Instagram-ready caption for a task set (caption only).

## Complete Post Generation (Video + Caption)

### Request Body

```json
{
  "task_set_id": "string",
  "include_audio": true,
  "video_duration_per_image": 3.0,
  "max_concurrent_downloads": 5,
  "export_timeout": 120,
  "include_hashtags": true,
  "max_hashtags": 15
}
```

### Parameters

- `task_set_id` (required): The ID of the task set to generate content for
- `include_audio` (optional): Whether to include audio in video (default: true)
- `video_duration_per_image` (optional): Seconds per image if no audio (default: 3.0)
- `max_concurrent_downloads` (optional): Max parallel downloads (default: 5)
- `export_timeout` (optional): Video export timeout in seconds (default: 120)
- `include_hashtags` (optional): Whether to include hashtags in caption (default: true)
- `max_hashtags` (optional): Maximum number of hashtags (5-30, default: 15)

### Response

```json
{
  "task_set_id": "string",
  "video_url": "https://minio.../video.mp4",
  "video_metadata": {
    "filename": "videos/task_set_123_abc.mp4",
    "size_bytes": 2048576,
    "duration_seconds": 15.5,
    "story_count": 5,
    "include_audio": true,
    "generated_at": "2025-01-14T10:30:00Z"
  },
  "caption": "Animals Learning Set",
  "hashtags": ["#NepaliApp", "#LearnNepali", "#NepaliLanguage", "..."],
  "full_caption": "Animals Learning Set\n\n#NepaliApp #LearnNepali...",
  "caption_metadata": {
    "generated_at": "2025-01-14T10:30:00Z",
    "task_set_title": "Animals Learning Set",
    "story_count": 5,
    "task_count": 10,
    "character_count": 150
  }
}
```

## Caption Only Generation

#### Request Body

```json
{
  "task_set_id": "string",
  "include_hashtags": true,
  "max_hashtags": 15
}
```

#### Parameters

- `task_set_id` (required): The ID of the task set to generate a caption for
- `include_hashtags` (optional): Whether to include hashtags in the full caption (default: true)
- `max_hashtags` (optional): Maximum number of hashtags to include (5-30, default: 15)

#### Response

```json
{
  "task_set_id": "string",
  "caption": "Animals Learning Set",
  "hashtags": ["#NepaliApp", "#LearnNepali", "#NepaliLanguage", "#Education", "..."],
  "full_caption": "Animals Learning Set\n\n#NepaliApp #LearnNepali #NepaliLanguage #Education...",
  "metadata": {
    "generated_at": "2025-01-14T10:30:00Z",
    "task_set_title": "Animals Learning Set",
    "story_count": 5,
    "task_count": 10,
    "character_count": 150,
    "usage_metadata": {
      "method": "simple_title_hashtags",
      "hashtag_count": 15
    }
  }
}
```

## Features

### Simple Caption Generation

- Uses the task set title as the main caption
- Adds predefined hashtags for better reach
- No complex AI generation - fast and reliable

### Hashtag Generation

- Uses predefined relevant hashtags
- Includes core tags: #NepaliApp #LearnNepali #NepaliLanguage
- Adds educational tags: #LanguageLearning #Education #Culture
- Includes engagement tags: #Interactive #Fun #Learning

## Setup Instructions

### Dependencies

The feature requires:
- FastAPI
- Pydantic
- MongoDB access

No additional setup required - works out of the box!

## Usage Examples

### Complete Post Generation (Recommended)

```python
import httpx

async def generate_complete_post():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/management/sharepost/generate_post",
            json={
                "task_set_id": "your_task_set_id",
                "include_audio": True,
                "max_hashtags": 15
            },
            headers={"Authorization": "Bearer your_token"},
            timeout=180.0  # 3 minutes for video generation
        )
        result = response.json()

        # You now have both video and caption!
        video_url = result["video_url"]
        full_caption = result["full_caption"]

        return result
```

### Caption Only Generation

```python
async def generate_caption_only():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/management/sharepost/captions/create_caption",
            json={
                "task_set_id": "your_task_set_id",
                "max_hashtags": 15
            },
            headers={"Authorization": "Bearer your_token"}
        )
        return response.json()
```

### Custom Options

```python
# Complete post with custom settings
request_data = {
    "task_set_id": "your_task_set_id",
    "include_audio": True,
    "video_duration_per_image": 4.0,  # 4 seconds per image
    "max_hashtags": 20,
    "include_hashtags": True,
    "export_timeout": 180  # 3 minutes timeout
}
```

## Integration with Existing Features

### One-Step Solution (Recommended)

Use the combined endpoint for the simplest workflow:

```python
# Single call gets you everything!
result = await generate_complete_post(task_set_id)
video_url = result["video_url"]
caption = result["full_caption"]
# Ready to post on Instagram!
```

### Two-Step Solution (If needed)

For more control, use separate endpoints:

1. Generate video using `/v1/management/sharepost/generate_video`
2. Generate caption using `/v1/management/sharepost/captions/create_caption`
3. Combine and share on social media

### Task Set Compatibility

Works with task sets from:
- Regular task_sets collection
- Curated content (curated_content_set collection)
- Both story-based and task-based content

## Error Handling

The API handles various error scenarios:

- **404**: Task set not found
- **500**: Gemini API unavailable or misconfigured
- **400**: Invalid request parameters
- **500**: Database connection issues

## Performance Considerations

- Caption generation typically takes 2-5 seconds
- Uses Gemini 2.0 Flash model for optimal speed
- Caches prompt templates in database
- Limits content analysis to first 5 tasks/stories for performance

## Testing

Use the provided test script:

```bash
python test_caption_generation.py
```

Make sure to:
1. Update the task_set_id in the test script
2. Add proper authentication headers
3. Ensure the application is running

## Customization

### Custom Prompts

Modify the prompt in the database:

```javascript
db.prompts.updateOne(
  {"name": "instagram_caption_prompt"},
  {"$set": {"prompt": "Your custom prompt template..."}}
)
```

### Additional Styles

Add new caption styles by modifying the prompt template and updating the API validation.

## Security

- Requires proper authentication
- Validates user access to task sets
- Sanitizes input parameters
- Uses secure database queries

## Monitoring

Monitor the feature through:
- API response times
- Gemini API usage metrics
- Error rates and types
- Generated caption quality
