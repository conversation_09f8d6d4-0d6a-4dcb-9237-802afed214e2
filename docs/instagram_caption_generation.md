# Instagram Caption Generation

This document describes the Instagram caption generation feature that creates engaging, shareable captions for Nepali learning content.

## Overview

The Instagram caption generation feature automatically creates social media-ready captions for task sets, complete with relevant hashtags and engaging content that promotes Nepali language learning.

## API Endpoint

### Create Instagram Caption

**POST** `/v1/management/sharepost/captions/create_caption`

Creates an Instagram-ready caption for a task set.

#### Request Body

```json
{
  "task_set_id": "string",
  "caption_style": "engaging",
  "include_hashtags": true,
  "max_hashtags": 15,
  "language": "mixed"
}
```

#### Parameters

- `task_set_id` (required): The ID of the task set to generate a caption for
- `caption_style` (optional): Style of caption - "engaging", "educational", "fun", or "motivational" (default: "engaging")
- `include_hashtags` (optional): Whether to include hashtags in the full caption (default: true)
- `max_hashtags` (optional): Maximum number of hashtags to generate (5-30, default: 15)
- `language` (optional): Primary language - "nepali", "english", or "mixed" (default: "nepali")

#### Response

```json
{
  "task_set_id": "string",
  "caption": "Main caption text...",
  "hashtags": ["#NepaliLanguage", "#LearnNepali", "#Education", "..."],
  "full_caption": "Caption text with hashtags...",
  "metadata": {
    "generated_at": "2025-01-14T10:30:00Z",
    "task_set_title": "Animals Learning Set",
    "story_count": 5,
    "task_count": 10,
    "caption_style": "engaging",
    "language": "mixed",
    "character_count": 1250,
    "usage_metadata": {
      "prompt_tokens": 500,
      "completion_tokens": 200,
      "total_tokens": 700
    }
  }
}
```

## Features

### Caption Styles

1. **Engaging**: Uses exciting language, emojis, and call-to-action
2. **Educational**: Focuses on learning benefits and knowledge sharing
3. **Fun**: Uses playful language and emphasizes enjoyment
4. **Motivational**: Inspires learners and highlights achievements

### Language Options

1. **Nepali**: Caption primarily in Nepali with English hashtags
2. **English**: Caption in English with focus on Nepali learning
3. **Mixed**: Combination of Nepali and English for broader reach

### Hashtag Generation

- Automatically generates relevant hashtags
- Includes core tags: #NepaliLanguage #LearnNepali #Education
- Adds educational tags: #LanguageLearning #KidsEducation
- Includes cultural tags: #NepalCulture #SouthAsian
- Uses engagement tags: #Interactive #Fun

## Setup Instructions

### 1. Add Prompt to Database

Run the setup script to add the Instagram caption prompt:

```bash
python scripts/add_instagram_caption_prompt.py
```

### 2. Environment Requirements

Ensure you have the required environment variables:

```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Dependencies

The feature requires:
- Google Generative AI library
- FastAPI
- Pydantic
- MongoDB access

## Usage Examples

### Basic Caption Generation

```python
import httpx

async def generate_caption():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/management/sharepost/captions/create_caption",
            json={
                "task_set_id": "your_task_set_id",
                "caption_style": "engaging"
            },
            headers={"Authorization": "Bearer your_token"}
        )
        return response.json()
```

### Custom Style and Language

```python
request_data = {
    "task_set_id": "your_task_set_id",
    "caption_style": "educational",
    "language": "mixed",
    "max_hashtags": 20,
    "include_hashtags": True
}
```

## Integration with Existing Features

### Video Generation Integration

The caption generation works seamlessly with the existing video generation feature:

1. Generate video using `/v1/management/sharepost/generate_video`
2. Generate caption using `/v1/management/sharepost/captions/create_caption`
3. Share both video and caption on social media

### Task Set Compatibility

Works with task sets from:
- Regular task_sets collection
- Curated content (curated_content_set collection)
- Both story-based and task-based content

## Error Handling

The API handles various error scenarios:

- **404**: Task set not found
- **500**: Gemini API unavailable or misconfigured
- **400**: Invalid request parameters
- **500**: Database connection issues

## Performance Considerations

- Caption generation typically takes 2-5 seconds
- Uses Gemini 2.0 Flash model for optimal speed
- Caches prompt templates in database
- Limits content analysis to first 5 tasks/stories for performance

## Testing

Use the provided test script:

```bash
python test_caption_generation.py
```

Make sure to:
1. Update the task_set_id in the test script
2. Add proper authentication headers
3. Ensure the application is running

## Customization

### Custom Prompts

Modify the prompt in the database:

```javascript
db.prompts.updateOne(
  {"name": "instagram_caption_prompt"},
  {"$set": {"prompt": "Your custom prompt template..."}}
)
```

### Additional Styles

Add new caption styles by modifying the prompt template and updating the API validation.

## Security

- Requires proper authentication
- Validates user access to task sets
- Sanitizes input parameters
- Uses secure database queries

## Monitoring

Monitor the feature through:
- API response times
- Gemini API usage metrics
- Error rates and types
- Generated caption quality
