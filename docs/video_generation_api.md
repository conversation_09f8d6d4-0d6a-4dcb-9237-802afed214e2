# Video Generation API Documentation

## Overview

The Video Generation API allows you to create slideshow videos from task-sets by combining images and audio from stories stored in the database. The API fetches media URLs from MinIO, downloads the content, creates a video using MoviePy, and uploads the result back to MinIO.

## Features

- ✅ **Proper Authentication**: Uses `get_tenant_info` for JWT-based authentication
- ✅ **Database Integration**: Fetches task-sets and stories from MongoDB
- ✅ **MinIO Integration**: Downloads images/audio and uploads generated videos
- ✅ **Video Processing**: Creates slideshow videos using MoviePy
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Status Tracking**: Video generation status and metadata storage
- ✅ **Preview Support**: Preview available media before generation

## API Endpoints

### 1. Generate Video

**Endpoint:** `POST /v1/management/sharepost/generate_video`

**Description:** Generate a slideshow video from task set stories with images and audio.

**Request Body:**
```json
{
  "task_set_id": "string",
  "include_audio": true,
  "video_duration_per_image": 3.0
}
```

**Parameters:**
- `task_set_id` (required): The ID of the task set containing stories
- `include_audio` (optional, default: true): Whether to include audio in the video
- `video_duration_per_image` (optional, default: 3.0): Duration per image when no audio is available

**Response:**
```json
{
  "video_url": "https://minio.nextai.asia/bucket/path/video.mp4",
  "video_metadata": {
    "object_name": "slideshow_tasksetid_12345678.mp4",
    "bucket_name": "nepali-app-media",
    "url": "https://minio.nextai.asia/bucket/path/video.mp4",
    "content_type": "video/mp4",
    "size_bytes": 15420000,
    "task_set_id": "60f1b2b3c4d5e6f7a8b9c0d1",
    "user_id": "60f1b2b3c4d5e6f7a8b9c0d2",
    "stories_count": 5,
    "include_audio": true,
    "video_duration_per_image": 3.0,
    "generation_timestamp": "2024-01-15T10:30:00Z",
    "stories_processed": [
      {
        "stage": 1,
        "script_preview": "नेपालको राजधानी काठमाडौं हो...",
        "has_audio": true,
        "has_image": true
      }
    ]
  },
  "processing_status": "completed"
}
```

### 2. Preview Task Set Media

**Endpoint:** `GET /v1/management/sharepost/task_set/{task_set_id}/preview`

**Description:** Preview the media content available in a task set for video generation.

**Response:**
```json
{
  "task_set_id": "60f1b2b3c4d5e6f7a8b9c0d1",
  "total_stories": 5,
  "stories_with_images": 4,
  "stories_with_audio": 3,
  "stories_preview": [
    {
      "stage": 1,
      "script_preview": "नेपालको राजधानी काठमाडौं हो...",
      "has_image": true,
      "has_audio": true,
      "image_url": "https://minio.nextai.asia/bucket/image1.jpg",
      "audio_url": "https://minio.nextai.asia/bucket/audio1.mp3"
    }
  ]
}
```

### 3. Check Video Status

**Endpoint:** `GET /v1/management/sharepost/video_status/{task_set_id}`

**Description:** Get the status of generated videos for a task set.

**Response:**
```json
[
  {
    "video_id": "60f1b2b3c4d5e6f7a8b9c0d3",
    "status": "completed",
    "created_at": "2024-01-15T10:30:00Z",
    "video_url": "https://minio.nextai.asia/bucket/video.mp4",
    "stories_count": 5,
    "include_audio": true,
    "file_size_bytes": 15420000
  }
]
```

## Authentication

All endpoints require JWT authentication using the standard authentication system:

```bash
curl -X POST "http://localhost:8000/v1/management/sharepost/generate_video" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_set_id": "60f1b2b3c4d5e6f7a8b9c0d1",
    "include_audio": true,
    "video_duration_per_image": 3.0
  }'
```

## Data Requirements

### Task Set Structure
The task set must exist in the `task_sets` collection and contain:
```json
{
  "_id": "ObjectId",
  "user_id": "string",
  "stories": ["story_id_1", "story_id_2", ...],
  // ... other fields
}
```

### Story Structure
Stories must exist in the `story_steps` collection with:
```json
{
  "_id": "ObjectId",
  "stage": 1,
  "script": "Story text in Nepali",
  "image_metadata": {
    "url": "https://minio.nextai.asia/bucket/image.jpg",
    "bucket_name": "nepali-app-media",
    "object_name": "path/image.jpg"
  },
  "audio_metadata": {
    "url": "https://minio.nextai.asia/bucket/audio.mp3",
    "bucket_name": "nepali-app-media", 
    "object_name": "path/audio.mp3"
  }
}
```

## Video Generation Process

1. **Validation**: Verify task set exists and user has access
2. **Story Fetching**: Retrieve stories associated with the task set
3. **Media Validation**: Check for valid image URLs (required) and audio URLs (optional)
4. **Download**: Download images and audio files from MinIO to temporary directory
5. **Video Creation**: Use MoviePy to create slideshow video:
   - Each image becomes a video clip
   - Audio duration determines clip length (if available)
   - Fallback to `video_duration_per_image` if no audio
   - Clips are concatenated into final video
6. **Upload**: Save generated video to MinIO
7. **Metadata Storage**: Store video metadata in `generated_videos` collection
8. **Cleanup**: Remove temporary files

## Error Handling

The API provides comprehensive error handling:

- **404**: Task set not found
- **400**: No stories found or invalid media URLs
- **401**: Authentication required
- **500**: Video generation or processing errors

Example error response:
```json
{
  "detail": "No stories with valid images found in task set"
}
```

## Dependencies

The video generation feature requires:

- **MoviePy**: Video processing library (already in pyproject.toml)
- **Pillow**: Image processing (already in pyproject.toml)
- **httpx**: HTTP client for downloading media (already in pyproject.toml)
- **MinIO**: Object storage for media files
- **MongoDB**: Database for task sets and stories

## Performance Considerations

- **Temporary Storage**: Uses system temporary directory for processing
- **Memory Usage**: Large images/audio files may require significant memory
- **Processing Time**: Video generation time depends on:
  - Number of stories/images
  - Image resolution and file sizes
  - Audio file lengths
  - Server CPU performance

## Testing

Use the provided test script to verify functionality:

```bash
python test_video_generation.py
```

The test script checks:
- API endpoint availability
- Authentication requirements
- Response format validation
- Error handling

## Usage Examples

### Basic Video Generation
```python
import httpx

async def generate_video(task_set_id: str, jwt_token: str):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/management/sharepost/generate_video",
            json={
                "task_set_id": task_set_id,
                "include_audio": True,
                "video_duration_per_image": 3.0
            },
            headers={"Authorization": f"Bearer {jwt_token}"}
        )
        return response.json()
```

### Preview Before Generation
```python
async def preview_media(task_set_id: str, jwt_token: str):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8000/v1/management/sharepost/task_set/{task_set_id}/preview",
            headers={"Authorization": f"Bearer {jwt_token}"}
        )
        preview = response.json()
        print(f"Stories with images: {preview['stories_with_images']}")
        print(f"Stories with audio: {preview['stories_with_audio']}")
        return preview
```

## Troubleshooting

### Common Issues

1. **"Task set not found"**: Verify the task_set_id exists in the database
2. **"No stories with valid images"**: Check that stories have image_metadata.url
3. **"Failed to download media"**: Verify MinIO URLs are accessible
4. **"Video generation failed"**: Check server logs for MoviePy errors

### Debugging

Enable detailed logging by checking the application logs:
```bash
# Check management service logs
docker logs nepali_app-management-1
```

The video generation process logs detailed information about:
- Story fetching and validation
- Media download progress
- Video creation steps
- Upload and metadata storage
