# Multi-stage build for Nepali App microservices using uv
# First, build the application in the `/app` directory
FROM python:3.10-slim AS builder

# Install uv for faster dependency installation
RUN pip install --no-cache-dir uv==0.1.24

# Set environment variables for uv
ENV UV_COMPILE_BYTECODE=1 UV_LINK_MODE=copy
ENV UV_PYTHON_INSTALL_DIR=/python
ENV UV_PYTHON_PREFERENCE=only-managed

# Install build dependencies for packages like argon2-cffi
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY pyproject.toml uv.lock ./

# Install dependencies with caching
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system .

# Copy application code
COPY . .

# Then, use a final image without build dependencies
FROM python:3.10-slim

# Install runtime packages including FFmpeg for MoviePy
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    libffi8 \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgtk-3-0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r app && useradd -r -g app app

# Copy the Python packages from builder
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy the application from the builder
COPY --from=builder --chown=app:app /app /app

# Make sure the README.md file is accessible
RUN if [ -f /app/app/v1/api/README.md ]; then \
    chmod 644 /app/app/v1/api/README.md; \
else \
    mkdir -p /app/app/v1/api; \
    echo "# Nepali App API" > /app/app/v1/api/README.md; \
    chown -R app:app /app/app/v1/api; \
    chmod 644 /app/app/v1/api/README.md; \
fi

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Expose port
EXPOSE 8000

# Health check with increased start period
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD curl --fail http://localhost:8000/health || exit 1

# Switch to non-root user
USER app
