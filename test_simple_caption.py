#!/usr/bin/env python3
"""
Simple test for the caption generation function without API calls.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def test_simple_caption_generation():
    """Test the simple caption generation function directly."""
    
    # Mock content data
    mock_content_data = {
        "task_set": {
            "title": "Animals Learning Set",
            "_id": "test_id_123"
        },
        "stories": [
            {"stage": 1, "script": "गाई भन्छ मो मो"},
            {"stage": 2, "script": "कुकुर भन्छ भु भु"}
        ],
        "tasks": [
            {"type": "single_choice", "question": {"text": "गाईले के भन्छ?"}},
            {"type": "multiple_choice", "question": {"text": "कुन जनावर छ?"}}
        ]
    }
    
    try:
        # Import the function
        from app.v1.api.management_service.routes.sharepost.create_captions import generate_simple_caption
        
        # Test the function
        result = generate_simple_caption(
            content_data=mock_content_data,
            max_hashtags=10
        )
        
        print("✅ Caption generation test successful!")
        print(f"Caption: {result['caption']}")
        print(f"Hashtags: {result['hashtags']}")
        print(f"Metadata: {result['usage_metadata']}")
        
        # Verify the results
        assert result['caption'] == "Animals Learning Set"
        assert len(result['hashtags']) <= 10
        assert "#NepaliApp" in result['hashtags']
        assert "#LearnNepali" in result['hashtags']
        assert "#NepaliLanguage" in result['hashtags']
        
        print("✅ All assertions passed!")
        
        # Test with no title
        mock_content_no_title = {
            "task_set": {},
            "stories": [],
            "tasks": []
        }
        
        result_no_title = generate_simple_caption(
            content_data=mock_content_no_title,
            max_hashtags=5
        )
        
        print(f"\nTest with no title:")
        print(f"Caption: {result_no_title['caption']}")
        print(f"Hashtags: {result_no_title['hashtags']}")
        
        assert result_no_title['caption'] == "Nepali Learning Content"
        assert len(result_no_title['hashtags']) <= 5
        
        print("✅ No title test passed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure the application modules are available")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🧪 Testing Simple Caption Generation Function...")
    test_simple_caption_generation()
