#!/usr/bin/env python3
"""
Test script for Instagram caption generation functionality.

This script tests the caption generation endpoint to ensure it works correctly.
"""

import asyncio
import httpx
import json
import os
from typing import Dict, Any


async def test_caption_generation():
    """Test the Instagram caption generation endpoint."""
    
    # Configuration
    BASE_URL = "http://localhost:8000"  # Adjust as needed
    ENDPOINT = "/v1/management/sharepost/captions/create_caption"
    
    # Test data - you'll need to replace with actual task_set_id from your database
    test_request = {
        "task_set_id": "YOUR_TASK_SET_ID_HERE",  # Replace with actual task set ID
        "caption_style": "engaging",
        "include_hashtags": True,
        "max_hashtags": 15,
        "language": "mixed"
    }
    
    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers here if needed
        # "Authorization": "Bearer YOUR_TOKEN_HERE"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"Testing caption generation endpoint: {BASE_URL}{ENDPOINT}")
            print(f"Request data: {json.dumps(test_request, indent=2)}")
            
            response = await client.post(
                f"{BASE_URL}{ENDPOINT}",
                json=test_request,
                headers=headers,
                timeout=60.0  # 60 second timeout
            )
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Caption generation successful!")
                print(f"Generated caption: {result.get('caption', 'N/A')}")
                print(f"Hashtags: {result.get('hashtags', [])}")
                print(f"Full caption length: {len(result.get('full_caption', ''))}")
                print(f"Metadata: {json.dumps(result.get('metadata', {}), indent=2)}")
            else:
                print(f"❌ Caption generation failed!")
                print(f"Error response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing caption generation: {str(e)}")


def print_usage():
    """Print usage instructions."""
    print("""
Instagram Caption Generation Test Script

Before running this test:

1. Make sure your application is running on localhost:8000
2. Replace 'YOUR_TASK_SET_ID_HERE' with an actual task set ID from your database
3. Add proper authentication headers if required
4. Ensure the Instagram caption prompt is added to the database

To add the prompt to database, run:
    python scripts/add_instagram_caption_prompt.py

To test the endpoint:
    python test_caption_generation.py

Example task set IDs you can try:
- Check your database for existing task sets
- Use the management service to list task sets
- Create a new task set through the application

The endpoint will:
1. Fetch the task set and its stories/tasks
2. Generate an engaging Instagram caption
3. Create relevant hashtags
4. Return the complete caption ready for social media
""")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        print_usage()
    else:
        print("🧪 Testing Instagram Caption Generation...")
        asyncio.run(test_caption_generation())
