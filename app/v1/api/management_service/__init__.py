"""
Management Service - Consolidated CRUD Operations

This service handles ALL non-real-time functionality:
- Task sets management (CRUD operations)
- Task items management
- Submissions and scoring
- Media file handling
- User statistics and leaderboards

Real-time Socket.IO functionality has been moved to the Socket Service.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse

from app.shared.utils.logger import setup_new_logging

# Import all route modules with folder-based organization
from app.v1.api.management_service.routes.tasks.task_sets import router as task_sets_router
from app.v1.api.management_service.routes.tasks.task_items import router as task_items_router
from app.v1.api.management_service.routes.tasks.submissions import router as submissions_router
from app.v1.api.management_service.routes.scoring.scoring import router as scoring_router
from app.v1.api.management_service.routes.scoring.leaderboard import router as leaderboard_router
from app.v1.api.management_service.routes.media.files import router as media_router
from app.v1.api.management_service.routes.tasks.story import router as story_router
from app.v1.api.management_service.routes.curated import router as curated_contents_router
from app.v1.api.management_service.routes.editor import router as editor_router

from app.v1.api.management_service.routes.sharepost import router as sharepost_router
from app.v1.api.management_service.routes.admin import router as admin_router


# Configure logging
logger = setup_new_logging(__name__)

# Create FastAPI instance
app = FastAPI(
    title="Nepali App - Management Service",
    description="Consolidated service for task management, submissions, scoring, and media handling",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v1/management/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    servers=[
        {
            "url": "/v1/management",
            "description": "Management Service API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Backward compatibility - include the same routers without /tasks/ prefix
app.include_router(task_sets_router, prefix="/task-sets", tags=["Task Sets "])
app.include_router(task_items_router, prefix="/task-items", tags=["Task Items "])
app.include_router(submissions_router, prefix="/submissions", tags=["Submissions"])
app.include_router(story_router, prefix="/story", tags=["Story"])

# Curated content routes
app.include_router(curated_contents_router, prefix="/curated", tags=["Curated Content"])

# Editor routes
app.include_router(editor_router, prefix="/editor", tags=["Editor"])

# Admin routes
app.include_router(admin_router, tags=["Admin Dashboard"])

# Other services (no change needed)
app.include_router(scoring_router, prefix="/scoring", tags=["Scoring"])
app.include_router(leaderboard_router, prefix="/scoring", tags=["Leaderboard"])
app.include_router(media_router, prefix="/media", tags=["Media"])

# Video generation routes
app.include_router(sharepost_router, prefix="/sharepost", tags=["Video Generation"])


# Custom OpenAPI schema generation to ensure correct server URLs
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/v1/management",
            "description": "Management Service API"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


@app.get("/health")
async def health_check():
    """Management service health check endpoint."""
    return {
        "status": "healthy",
        "service": "management_service",
        "features": [
            "task_sets",
            "task_items",
            "submissions",
            "scoring",
            "media_handling",
            "leaderboards"
        ]
    }


@app.get("/")
async def root():
    """Redirect to API documentation"""
    return RedirectResponse(url="/docs")


# Custom docs endpoints
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom Swagger UI for Management Service."""
    from fastapi.openapi.docs import get_swagger_ui_html
    return get_swagger_ui_html(
        openapi_url="/v1/management/openapi.json",
        title="Management Service API",
        swagger_favicon_url="/static/favicon.ico",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        oauth2_redirect_url="/v1/management/docs/oauth2-redirect",
    )


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """Custom ReDoc for Management Service."""
    from fastapi.openapi.docs import get_redoc_html
    return get_redoc_html(
        openapi_url="/v1/management/openapi.json",
        title="Management Service API",
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()
