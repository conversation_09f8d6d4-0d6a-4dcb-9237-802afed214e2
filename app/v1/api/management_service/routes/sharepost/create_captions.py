from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Any, List, Dict, Optional
import os
from datetime import datetime
from bson import ObjectId

# Import proper authentication and models
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

# Setup logging
loggers = setup_new_logging(__name__)

# Gemini AI imports
try:
    import google.generativeai as genai
    from google.generativeai import types
    GEMINI_AVAILABLE = True
except ImportError as e:
    loggers.warning(f"Gemini AI not available: {e}. Caption generation will not work.")
    GEMINI_AVAILABLE = False

router = APIRouter()

class CreateCaptionRequest(BaseModel):
    """Request model for creating Instagram captions."""
    task_set_id: str = Field(..., description="Task set ID to generate caption for")
    caption_style: Optional[str] = Field("engaging", description="Style of caption: engaging, educational, fun, motivational")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")
    max_hashtags: int = Field(15, ge=5, le=30, description="Maximum number of hashtags to include")
    language: Optional[str] = Field("nepali", description="Primary language for caption: nepali, english, or mixed")

class CreateCaptionResponse(BaseModel):
    """Response model for Instagram caption generation."""
    task_set_id: str
    caption: str
    hashtags: List[str]
    full_caption: str  # Caption + hashtags combined
    metadata: Dict[str, Any]


async def fetch_task_set_content(current_user: UserTenantDB, task_set_id: str) -> Dict[str, Any]:
    """Fetch task set and its associated content for caption generation."""
    try:
        # Fetch task set from both possible collections
        task_set = await current_user.async_db.task_sets.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            # Try curated_content_set collection (editor service)
            task_set = await current_user.async_db.curated_content_set.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            raise HTTPException(status_code=404, detail="Task set not found")

        # Get story IDs from task set
        story_ids = task_set.get("stories", [])
        stories = []

        if story_ids:
            # Convert string IDs to ObjectIds if needed
            story_object_ids = []
            for story_id in story_ids:
                if isinstance(story_id, str):
                    story_object_ids.append(ObjectId(story_id))
                else:
                    story_object_ids.append(story_id)

            # Fetch stories
            cursor = current_user.async_db.story_steps.find({"_id": {"$in": story_object_ids}})
            stories = await cursor.to_list(length=None)

        # Get task IDs and fetch some sample tasks
        task_ids = task_set.get("tasks", [])
        tasks = []

        if task_ids:
            # Convert string IDs to ObjectIds if needed
            task_object_ids = []
            for task_id in task_ids[:5]:  # Limit to first 5 tasks for caption context
                if isinstance(task_id, str):
                    task_object_ids.append(ObjectId(task_id))
                else:
                    task_object_ids.append(task_id)

            # Fetch tasks
            cursor = current_user.async_db.task_items.find({"_id": {"$in": task_object_ids}})
            tasks = await cursor.to_list(length=None)

        return {
            "task_set": task_set,
            "stories": stories,
            "tasks": tasks
        }

    except Exception as e:
        loggers.error(f"Error fetching task set content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch task set content: {str(e)}")


async def generate_instagram_caption(
    content_data: Dict[str, Any],
    caption_style: str,
    language: str,
    max_hashtags: int,
    current_user: UserTenantDB
) -> Dict[str, Any]:
    """Generate Instagram caption using Gemini AI."""
    try:
        if not GEMINI_AVAILABLE:
            raise HTTPException(
                status_code=500,
                detail="Caption generation is not available. Gemini AI library is not properly installed."
            )

        # Check if API key is available
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=500,
                detail="Gemini API key not configured"
            )

        client = genai.Client(api_key=api_key)

        # Fetch caption generation prompt from database
        prompt_data = await current_user.async_db.prompts.find_one({"name": "instagram_caption_prompt"})

        if not prompt_data:
            # Use default prompt if not found in database
            base_prompt = """You are a social media expert creating engaging Instagram captions for educational content.

Create an Instagram-ready caption for this Nepali learning content:

CONTENT SUMMARY:
- Task Set Title: {title}
- Number of Stories: {story_count}
- Number of Tasks: {task_count}
- Content Type: {content_type}
- Stories: {stories_summary}
- Tasks: {tasks_summary}

CAPTION REQUIREMENTS:
- Style: {caption_style}
- Language: {language} (use Nepali for main content, English for hashtags if mixed)
- Make it engaging and shareable
- Include emojis appropriately
- Keep it under 2200 characters
- Focus on learning benefits and cultural value

HASHTAG REQUIREMENTS:
- Generate exactly {max_hashtags} relevant hashtags
- Mix of Nepali learning, education, culture, and trending tags
- Include #NepaliLanguage #LearnNepali #Education
- Use English for hashtags for better reach

Return your response in this exact JSON format:
{{
  "caption": "Main caption text here",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3", ...]
}}"""
        else:
            base_prompt = prompt_data.get("prompt", "")

        # Prepare content summary
        task_set = content_data["task_set"]
        stories = content_data["stories"]
        tasks = content_data["tasks"]

        # Create stories summary
        stories_summary = []
        for story in stories[:3]:  # Limit to first 3 stories
            script = story.get("script", "")[:100]  # First 100 chars
            stories_summary.append(f"Stage {story.get('stage', 'N/A')}: {script}...")

        # Create tasks summary
        tasks_summary = []
        for task in tasks[:3]:  # Limit to first 3 tasks
            question = task.get("question", {})
            task_type = task.get("type", "unknown")
            if isinstance(question, dict):
                question_text = question.get("text", question.get("question", ""))[:80]
            else:
                question_text = str(question)[:80]
            tasks_summary.append(f"{task_type}: {question_text}...")

        # Format the prompt
        formatted_prompt = base_prompt.format(
            title=task_set.get("title", "Nepali Learning Content"),
            story_count=len(stories),
            task_count=len(tasks),
            content_type=task_set.get("input_type", "mixed"),
            stories_summary="; ".join(stories_summary) if stories_summary else "No stories available",
            tasks_summary="; ".join(tasks_summary) if tasks_summary else "No tasks available",
            caption_style=caption_style,
            language=language,
            max_hashtags=max_hashtags
        )

        # Generate content using Gemini
        model = "gemini-2.0-flash"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=formatted_prompt),
                ],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            temperature=0.7,
            response_mime_type="application/json",
        )

        loggers.info(f"Generating Instagram caption for task set: {task_set.get('_id')}")

        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        # Parse the response
        import json
        result = json.loads(response.text)

        return {
            "caption": result.get("caption", ""),
            "hashtags": result.get("hashtags", []),
            "usage_metadata": {
                "prompt_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                "completion_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0) if hasattr(response, 'usage_metadata') else 0,
                "total_tokens": getattr(response.usage_metadata, 'total_token_count', 0) if hasattr(response, 'usage_metadata') else 0
            }
        }

    except json.JSONDecodeError as e:
        loggers.error(f"Failed to parse Gemini response as JSON: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate valid caption format")
    except Exception as e:
        loggers.error(f"Error generating Instagram caption: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Caption generation failed: {str(e)}")


@router.post("/create_caption", response_model=CreateCaptionResponse)
async def create_instagram_caption(
    request: CreateCaptionRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create an Instagram-ready caption for a task set.

    This endpoint:
    1. Fetches the task set and associated stories/tasks from the database
    2. Analyzes the content to understand the learning material
    3. Uses Gemini AI to generate an engaging Instagram caption
    4. Creates relevant hashtags for better reach
    5. Returns the complete caption ready for social media sharing

    Args:
        request: Caption generation request with task_set_id and options
        current_user: Current authenticated user with database access

    Returns:
        CreateCaptionResponse with generated caption, hashtags, and metadata
    """
    try:
        loggers.info(f"Creating Instagram caption for task set: {request.task_set_id}")

        # Fetch task set content
        content_data = await fetch_task_set_content(current_user, request.task_set_id)

        # Generate caption using Gemini AI
        caption_result = await generate_instagram_caption(
            content_data=content_data,
            caption_style=request.caption_style,
            language=request.language,
            max_hashtags=request.max_hashtags,
            current_user=current_user
        )

        # Prepare hashtags (ensure they start with #)
        hashtags = []
        for tag in caption_result["hashtags"]:
            if not tag.startswith("#"):
                hashtags.append(f"#{tag}")
            else:
                hashtags.append(tag)

        # Create full caption (caption + hashtags)
        full_caption = caption_result["caption"]
        if request.include_hashtags and hashtags:
            full_caption += "\n\n" + " ".join(hashtags)

        # Prepare metadata
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "task_set_title": content_data["task_set"].get("title", ""),
            "story_count": len(content_data["stories"]),
            "task_count": len(content_data["tasks"]),
            "caption_style": request.caption_style,
            "language": request.language,
            "character_count": len(full_caption),
            "usage_metadata": caption_result.get("usage_metadata", {})
        }

        loggers.info(f"Successfully generated Instagram caption for task set: {request.task_set_id}")

        return CreateCaptionResponse(
            task_set_id=request.task_set_id,
            caption=caption_result["caption"],
            hashtags=hashtags,
            full_caption=full_caption,
            metadata=metadata
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in caption generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Caption generation failed: {str(e)}"
        )