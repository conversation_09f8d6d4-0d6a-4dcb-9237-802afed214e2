from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Any, List, Dict, Optional
import os
from datetime import datetime
from bson import ObjectId

# Import proper authentication and models
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

# Setup logging
loggers = setup_new_logging(__name__)

# Static caption templates - no LLM needed
STATIC_CAPTIONS = {
    "engaging": [
        "🌟 Discover the beauty of Nepali language! Join thousands learning with our interactive app. Every word tells a story, every lesson brings you closer to Nepal's rich culture! 🇳🇵✨",
        "📚 Ready to master Nepali? Our app makes learning fun and easy! From basic words to cultural stories - your journey to fluency starts here! 🚀🎯",
        "🎉 Learning Nepali has never been this exciting! Interactive lessons, engaging stories, and cultural insights all in one place. Start your adventure today! 🌈📖"
    ],
    "educational": [
        "📖 Expand your linguistic horizons with our comprehensive Nepali learning platform. Structured lessons designed for effective language acquisition and cultural understanding. 🎓🌍",
        "🧠 Evidence-based learning methods meet traditional Nepali culture. Our app combines modern pedagogy with authentic cultural content for optimal learning outcomes. 📊✅",
        "📚 Master Nepali through systematic learning modules. From vocabulary building to cultural immersion - every lesson is crafted for educational excellence. 🎯📈"
    ],
    "fun": [
        "🎮 Who says learning can't be fun? Dive into Nepali with games, stories, and interactive challenges! Learning feels like playing! 🎪🎨",
        "😄 Giggles guaranteed while learning Nepali! Our fun-filled lessons make every study session an adventure. Ready to smile your way to fluency? 🌟🎭",
        "🎊 Turn study time into play time! Learn Nepali through entertaining activities and engaging content. Education has never been this enjoyable! 🎈🎯"
    ],
    "motivational": [
        "💪 Every expert was once a beginner. Start your Nepali learning journey today and unlock new opportunities! Your future multilingual self will thank you. 🌟🚀",
        "🏆 Challenge yourself to learn something new! Nepali language skills open doors to rich culture, new friendships, and amazing opportunities. You've got this! 💫⭐",
        "🌱 Growth happens outside your comfort zone. Take the first step in your Nepali learning adventure. Every small progress counts towards fluency! 🎯💯"
    ]
}

STATIC_HASHTAGS = [
    "#NepaliApp", "#LearnNepali", "#NepaliLanguage", "#LanguageLearning",
    "#Nepal", "#Education", "#Culture", "#Learning", "#Mobile", "#App",
    "#Interactive", "#Fun", "#Educational", "#Multilingual", "#SouthAsia",
    "#Heritage", "#Traditional", "#Modern", "#Technology", "#Progress"
]

router = APIRouter()

class CreateCaptionRequest(BaseModel):
    """Request model for creating Instagram captions."""
    task_set_id: str = Field(..., description="Task set ID to generate caption for")
    caption_style: Optional[str] = Field("engaging", description="Style of caption: engaging, educational, fun, motivational")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")
    max_hashtags: int = Field(15, ge=5, le=30, description="Maximum number of hashtags to include")
    language: Optional[str] = Field("nepali", description="Primary language for caption: nepali, english, or mixed")

class CreateCaptionResponse(BaseModel):
    """Response model for Instagram caption generation."""
    task_set_id: str
    caption: str
    hashtags: List[str]
    full_caption: str  # Caption + hashtags combined
    metadata: Dict[str, Any]


async def fetch_task_set_content(current_user: UserTenantDB, task_set_id: str) -> Dict[str, Any]:
    """Fetch task set and its associated content for caption generation."""
    try:
        # Fetch task set from both possible collections
        task_set = await current_user.async_db.task_sets.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            # Try curated_content_set collection (editor service)
            task_set = await current_user.async_db.curated_content_set.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            raise HTTPException(status_code=404, detail="Task set not found")

        # Get story IDs from task set
        story_ids = task_set.get("stories", [])
        stories = []

        if story_ids:
            # Convert string IDs to ObjectIds if needed
            story_object_ids = []
            for story_id in story_ids:
                if isinstance(story_id, str):
                    story_object_ids.append(ObjectId(story_id))
                else:
                    story_object_ids.append(story_id)

            # Fetch stories
            cursor = current_user.async_db.story_steps.find({"_id": {"$in": story_object_ids}})
            stories = await cursor.to_list(length=None)

        # Get task IDs and fetch some sample tasks
        task_ids = task_set.get("tasks", [])
        tasks = []

        if task_ids:
            # Convert string IDs to ObjectIds if needed
            task_object_ids = []
            for task_id in task_ids[:5]:  # Limit to first 5 tasks for caption context
                if isinstance(task_id, str):
                    task_object_ids.append(ObjectId(task_id))
                else:
                    task_object_ids.append(task_id)

            # Fetch tasks
            cursor = current_user.async_db.task_items.find({"_id": {"$in": task_object_ids}})
            tasks = await cursor.to_list(length=None)

        return {
            "task_set": task_set,
            "stories": stories,
            "tasks": tasks
        }

    except Exception as e:
        loggers.error(f"Error fetching task set content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch task set content: {str(e)}")


def generate_static_instagram_caption(
    content_data: Dict[str, Any],
    caption_style: str,
    language: str,
    max_hashtags: int
) -> Dict[str, Any]:
    """Generate Instagram caption using static templates."""
    try:
        import random

        # Get task set info for context
        task_set = content_data["task_set"]
        stories = content_data["stories"]
        tasks = content_data["tasks"]

        # Select a random caption from the specified style
        style_captions = STATIC_CAPTIONS.get(caption_style, STATIC_CAPTIONS["engaging"])
        selected_caption = random.choice(style_captions)

        # Add task set context if available
        task_set_title = task_set.get("title", "")
        if task_set_title and len(task_set_title) > 0:
            # Add a line about the specific content
            content_line = f"\n\n📝 Current lesson: {task_set_title}"
            if len(stories) > 0:
                content_line += f" ({len(stories)} stories"
            if len(tasks) > 0:
                if len(stories) > 0:
                    content_line += f", {len(tasks)} tasks)"
                else:
                    content_line += f" ({len(tasks)} tasks)"
            else:
                if len(stories) > 0:
                    content_line += ")"

            selected_caption += content_line

        # Select hashtags
        selected_hashtags = STATIC_HASHTAGS[:max_hashtags] if max_hashtags <= len(STATIC_HASHTAGS) else STATIC_HASHTAGS

        # Shuffle hashtags for variety
        random.shuffle(selected_hashtags)
        selected_hashtags = selected_hashtags[:max_hashtags]

        # Ensure core hashtags are always included
        core_hashtags = ["#NepaliApp", "#LearnNepali", "#NepaliLanguage"]
        for core_tag in core_hashtags:
            if core_tag not in selected_hashtags:
                # Replace a random hashtag with core hashtag
                if len(selected_hashtags) >= max_hashtags:
                    selected_hashtags[random.randint(0, len(selected_hashtags)-1)] = core_tag
                else:
                    selected_hashtags.append(core_tag)

        return {
            "caption": selected_caption,
            "hashtags": selected_hashtags[:max_hashtags],
            "usage_metadata": {
                "method": "static_template",
                "style": caption_style,
                "template_count": len(style_captions),
                "hashtag_count": len(selected_hashtags)
            }
        }

    except Exception as e:
        loggers.error(f"Error generating static Instagram caption: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Caption generation failed: {str(e)}")


@router.post("/create_caption", response_model=CreateCaptionResponse)
async def create_instagram_caption(
    request: CreateCaptionRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create an Instagram-ready caption for a task set.

    This endpoint:
    1. Fetches the task set and associated stories/tasks from the database
    2. Analyzes the content to understand the learning material
    3. Uses Gemini AI to generate an engaging Instagram caption
    4. Creates relevant hashtags for better reach
    5. Returns the complete caption ready for social media sharing

    Args:
        request: Caption generation request with task_set_id and options
        current_user: Current authenticated user with database access

    Returns:
        CreateCaptionResponse with generated caption, hashtags, and metadata
    """
    try:
        loggers.info(f"Creating Instagram caption for task set: {request.task_set_id}")

        # Fetch task set content
        content_data = await fetch_task_set_content(current_user, request.task_set_id)

        # Generate caption using Gemini AI
        caption_result = await generate_instagram_caption(
            content_data=content_data,
            caption_style=request.caption_style,
            language=request.language,
            max_hashtags=request.max_hashtags,
            current_user=current_user
        )

        # Prepare hashtags (ensure they start with #)
        hashtags = []
        for tag in caption_result["hashtags"]:
            if not tag.startswith("#"):
                hashtags.append(f"#{tag}")
            else:
                hashtags.append(tag)

        # Create full caption (caption + hashtags)
        full_caption = caption_result["caption"]
        if request.include_hashtags and hashtags:
            full_caption += "\n\n" + " ".join(hashtags)

        # Prepare metadata
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "task_set_title": content_data["task_set"].get("title", ""),
            "story_count": len(content_data["stories"]),
            "task_count": len(content_data["tasks"]),
            "caption_style": request.caption_style,
            "language": request.language,
            "character_count": len(full_caption),
            "usage_metadata": caption_result.get("usage_metadata", {})
        }

        loggers.info(f"Successfully generated Instagram caption for task set: {request.task_set_id}")

        return CreateCaptionResponse(
            task_set_id=request.task_set_id,
            caption=caption_result["caption"],
            hashtags=hashtags,
            full_caption=full_caption,
            metadata=metadata
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in caption generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Caption generation failed: {str(e)}"
        )