from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Any, List, Dict, Optional
import os
from datetime import datetime
from bson import ObjectId

# Import proper authentication and models
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

# Setup logging
loggers = setup_new_logging(__name__)

# Static hashtags for captions
STATIC_HASHTAGS = [
    "#NepaliApp", "#LearnNepali", "#NepaliLanguage", "#LanguageLearning",
    "#Nepal", "#Education", "#Culture", "#Learning", "#Mobile", "#App",
    "#Interactive", "#Fun", "#Educational", "#Multilingual", "#SouthAsia",
    "#Heritage", "#Traditional", "#Modern", "#Technology", "#Progress"
]

router = APIRouter()

class CreateCaptionRequest(BaseModel):
    """Request model for creating Instagram captions."""
    task_set_id: str = Field(..., description="Task set ID to generate caption for")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")
    max_hashtags: int = Field(15, ge=5, le=30, description="Maximum number of hashtags to include")

class CreateCaptionResponse(BaseModel):
    """Response model for Instagram caption generation."""
    task_set_id: str
    caption: str
    hashtags: List[str]
    full_caption: str  # Caption + hashtags combined
    metadata: Dict[str, Any]


async def fetch_task_set_content(current_user: UserTenantDB, task_set_id: str) -> Dict[str, Any]:
    """Fetch task set and its associated content for caption generation."""
    try:
        # Fetch task set from both possible collections
        task_set = await current_user.async_db.task_sets.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            # Try curated_content_set collection (editor service)
            task_set = await current_user.async_db.curated_content_set.find_one({"_id": ObjectId(task_set_id)})

        if not task_set:
            raise HTTPException(status_code=404, detail="Task set not found")

        # Get story IDs from task set
        story_ids = task_set.get("stories", [])
        stories = []

        if story_ids:
            # Convert string IDs to ObjectIds if needed
            story_object_ids = []
            for story_id in story_ids:
                if isinstance(story_id, str):
                    story_object_ids.append(ObjectId(story_id))
                else:
                    story_object_ids.append(story_id)

            # Fetch stories
            cursor = current_user.async_db.story_steps.find({"_id": {"$in": story_object_ids}})
            stories = await cursor.to_list(length=None)

        # Get task IDs and fetch some sample tasks
        task_ids = task_set.get("tasks", [])
        tasks = []

        if task_ids:
            # Convert string IDs to ObjectIds if needed
            task_object_ids = []
            for task_id in task_ids[:5]:  # Limit to first 5 tasks for caption context
                if isinstance(task_id, str):
                    task_object_ids.append(ObjectId(task_id))
                else:
                    task_object_ids.append(task_id)

            # Fetch tasks
            cursor = current_user.async_db.task_items.find({"_id": {"$in": task_object_ids}})
            tasks = await cursor.to_list(length=None)

        return {
            "task_set": task_set,
            "stories": stories,
            "tasks": tasks
        }

    except Exception as e:
        loggers.error(f"Error fetching task set content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch task set content: {str(e)}")


def generate_simple_caption(
    content_data: Dict[str, Any],
    max_hashtags: int
) -> Dict[str, Any]:
    """Generate simple Instagram caption: task title + hashtags."""
    try:
        # Get task set info
        task_set = content_data["task_set"]

        # Use task title as caption, fallback to default if no title
        task_title = task_set.get("title", "Nepali Learning Content")
        caption = task_title

        # Select hashtags (limit to max_hashtags)
        selected_hashtags = STATIC_HASHTAGS[:max_hashtags] if max_hashtags <= len(STATIC_HASHTAGS) else STATIC_HASHTAGS

        return {
            "caption": caption,
            "hashtags": selected_hashtags,
            "usage_metadata": {
                "method": "simple_title_hashtags",
                "hashtag_count": len(selected_hashtags)
            }
        }

    except Exception as e:
        loggers.error(f"Error generating simple Instagram caption: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Caption generation failed: {str(e)}")


@router.post("/create_caption", response_model=CreateCaptionResponse)
async def create_instagram_caption(
    request: CreateCaptionRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create a simple Instagram caption for a task set.

    This endpoint:
    1. Fetches the task set from the database
    2. Uses the task title as the caption
    3. Adds predefined hashtags
    4. Returns the complete caption ready for social media sharing

    Args:
        request: Caption generation request with task_set_id and options
        current_user: Current authenticated user with database access

    Returns:
        CreateCaptionResponse with task title as caption, hashtags, and metadata
    """
    try:
        loggers.info(f"Creating Instagram caption for task set: {request.task_set_id}")

        # Fetch task set content
        content_data = await fetch_task_set_content(current_user, request.task_set_id)

        # Generate simple caption: task title + hashtags
        caption_result = generate_simple_caption(
            content_data=content_data,
            max_hashtags=request.max_hashtags
        )

        # Prepare hashtags (ensure they start with #)
        hashtags = []
        for tag in caption_result["hashtags"]:
            if not tag.startswith("#"):
                hashtags.append(f"#{tag}")
            else:
                hashtags.append(tag)

        # Create full caption (caption + hashtags)
        full_caption = caption_result["caption"]
        if request.include_hashtags and hashtags:
            full_caption += "\n\n" + " ".join(hashtags)

        # Prepare metadata
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "task_set_title": content_data["task_set"].get("title", ""),
            "story_count": len(content_data["stories"]),
            "task_count": len(content_data["tasks"]),
            "character_count": len(full_caption),
            "usage_metadata": caption_result.get("usage_metadata", {})
        }

        loggers.info(f"Successfully generated Instagram caption for task set: {request.task_set_id}")

        return CreateCaptionResponse(
            task_set_id=request.task_set_id,
            caption=caption_result["caption"],
            hashtags=hashtags,
            full_caption=full_caption,
            metadata=metadata
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in caption generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Caption generation failed: {str(e)}"
        )