"""
Task Utilities V2 for Socket Service V2 - SIMPLIFIED & CLEAN

Clean task processing with priority-based sequential generation:
1. Single choice tasks - generate options audio sequentially, return response
2. Multiple choice tasks - generate options audio sequentially
3. Image/audio tasks - generate media in background
4. Background processing for remaining tasks

Simplified architecture without excessive helper files.
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType, CollectionName

# Import core generation functions
from .prompt_maker_v2 import generate as generate_v2
from .audiogen import generate_audio
from .imagen import generate_image

logger = setup_new_logging(__name__)


# ============================================================================
# CORE AUDIO PROCESSING (from helpers/audio_processor.py)
# ============================================================================

async def process_audio_with_prompt_maker_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4
) -> Dict[str, Any]:
    """Process audio with the optimized V2 prompt maker."""
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker V2")

        # Call the V2 prompt maker with retry logic
        result = await _retry_with_exponential_backoff(
            generate_v2,
            audio_bytes,
            num_tasks,
            current_user
        )

        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker_v2.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "optimization_stats": {},
                "usage_metadata": {}
            }

        logger.info(f"✅ Generated {len(result['tasks'])} tasks with V2 optimizations")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with prompt_maker V2: {e}")
        return {
            "tasks": [],
            "error": str(e),
            "status": "error",
            "optimization_stats": {},
            "usage_metadata": {}
        }


# ============================================================================
# SIMPLIFIED TASK PROCESSING - MAIN FUNCTION
# ============================================================================

async def save_task_collection_and_items_with_priority(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Optional[Any] = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    SIMPLIFIED V2 Task processing with priority-based sequential generation.

    PRESERVED LOGIC:
    1. Single choice tasks - generate options audio sequentially, return response
    2. Multiple choice tasks - generate options audio sequentially
    3. Image/audio tasks - generate media in background
    4. Background processing for remaining tasks
    """
    try:
        logger.info("🎯 Starting SIMPLIFIED V2 task processing by type")

        tasks = tasks_data.get("tasks", [])
        if not tasks:
            return {"status": "error", "error": "No tasks to process"}

        # Save tasks to database first
        collection_id = await _save_tasks_to_database(
            current_user, session_id, tasks_data, collection_id, audio_storage_info
        )

        if not collection_id:
            return {"status": "error", "error": "Failed to save tasks to database"}

        # Process stories if they exist (PRESERVED LOGIC)
        stories = tasks_data.get("stories", [])
        story_collection_id = None
        if stories:
            story_collection_id = await _process_stories(
                current_user, session_id, stories, collection_id, audio_storage_info
            )

        # Categorize tasks by type (PRESERVED LOGIC)
        task_categories = _categorize_tasks_by_type(tasks)

        # Get first priority task (PRESERVED LOGIC)
        first_task = _get_first_priority_task(task_categories)
        first_task_completed = False

        # Process first priority task completely (PRESERVED LOGIC)
        if first_task:
            try:
                logger.info(f"🎯 PRIORITY: Processing first task {first_task.get('id')} completely")
                await _process_task_by_type(current_user, first_task, socketio_server)
                first_task_completed = True

                logger.info(f"✅ First task completed successfully")
            except Exception as e:
                logger.error(f"❌ Failed to complete first task: {e}")
                first_task_completed = False

        # Get remaining tasks for background processing (PRESERVED LOGIC)
        remaining_tasks = _get_remaining_tasks(task_categories, first_task)

        # Start background processing for remaining tasks (PRESERVED LOGIC)
        if remaining_tasks and use_background_tasks:
            logger.info(f"🔄 Starting background processing for {len(remaining_tasks)} remaining tasks")
            asyncio.create_task(_process_remaining_tasks_sequentially(
                current_user, remaining_tasks, socketio_server
            ))

        return {
            "status": "success",
            "task_set_id": str(collection_id),
            "collection_id": collection_id,
            "story_collection_id": story_collection_id,
            "first_task_completed": first_task_completed,
            "first_task": first_task if first_task_completed else None,
            "background_tasks_count": len(remaining_tasks),
            "task_categories": {k: len(v) for k, v in task_categories.items()},
            "stories_processed": len(stories) if stories else 0,
            "service_version": "v2",
            "priority_processing": True
        }

    except Exception as e:
        logger.error(f"❌ Error in simplified task processing: {e}")
        return {
            "status": "error",
            "error": str(e),
            "collection_metadata": {}
        }


# ============================================================================
# SIMPLIFIED HELPER FUNCTIONS (PRESERVED LOGIC)
# ============================================================================

async def _retry_with_exponential_backoff(func, *args, **kwargs):
    """Retry function with exponential backoff."""
    max_retries = 3
    initial_delay = 1.0
    max_delay = 30.0
    delay = initial_delay

    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
            await asyncio.sleep(delay)
            delay = min(delay * 2, max_delay)


def _categorize_tasks_by_type(tasks: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Categorize tasks by type for priority processing (PRESERVED LOGIC).

    Categories:
    1. single_choice - Single choice questions with options audio
    2. multiple_choice - Multiple choice questions with options audio
    3. image_audio - Tasks requiring image/audio media generation
    4. story - Story-based tasks
    """
    categories = {
        "single_choice": [],
        "multiple_choice": [],
        "image_audio": [],
        "story": []
    }

    for task in tasks:
        task_type = task.get("type", "single_choice")

        if task_type == "single_choice":
            categories["single_choice"].append(task)
        elif task_type == "multiple_choice":
            categories["multiple_choice"].append(task)
        elif task_type in ["image", "audio", "image_audio", "image_identification", "speak_word"]:
            categories["image_audio"].append(task)
        elif task_type == "story":
            categories["story"].append(task)
        else:
            # Default to single choice for unknown types
            logger.warning(f"Unknown task type '{task_type}', defaulting to single_choice")
            categories["single_choice"].append(task)

    # Log categorization results
    logger.info("📊 TASK CATEGORIZATION RESULTS:")
    for category, task_list in categories.items():
        if task_list:
            logger.info(f"  {category.upper()}: {len(task_list)} tasks")

    return categories


def _get_first_priority_task(task_categories: Dict[str, List[Dict[str, Any]]]) -> Optional[Dict[str, Any]]:
    """
    Get the first priority task based on type priority (PRESERVED LOGIC).

    Priority order:
    1. Single choice tasks
    2. Multiple choice tasks
    3. Image/audio tasks
    4. Story tasks
    """
    # Priority 1: Single choice tasks
    if task_categories["single_choice"]:
        first_task = task_categories["single_choice"][0]
        logger.info(f"🎯 PRIORITY 1: Selected single choice task {first_task.get('id')} as first task")
        return first_task

    # Priority 2: Multiple choice tasks
    if task_categories["multiple_choice"]:
        first_task = task_categories["multiple_choice"][0]
        logger.info(f"🎯 PRIORITY 2: Selected multiple choice task {first_task.get('id')} as first task")
        return first_task

    # Priority 3: Image/audio tasks
    if task_categories["image_audio"]:
        first_task = task_categories["image_audio"][0]
        logger.info(f"🎯 PRIORITY 3: Selected image/audio task {first_task.get('id')} as first task")
        return first_task

    # Priority 4: Story tasks
    if task_categories["story"]:
        first_task = task_categories["story"][0]
        logger.info(f"🎯 PRIORITY 4: Selected story task {first_task.get('id')} as first task")
        return first_task

    logger.warning("⚠️ No tasks found in any category")
    return None


def _get_remaining_tasks(
    task_categories: Dict[str, List[Dict[str, Any]]],
    first_task: Optional[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Get remaining tasks for background processing in CORRECT SEQUENTIAL ORDER."""
    remaining_tasks = []

    if not first_task:
        # If no first task was processed, return all tasks in priority order
        remaining_tasks.extend(task_categories["single_choice"])
        remaining_tasks.extend(task_categories["multiple_choice"])
        remaining_tasks.extend(task_categories["image_audio"])
        remaining_tasks.extend(task_categories["story"])
        logger.info(f"📋 REMAINING TASKS (no first task): {len(remaining_tasks)} tasks in priority order")
        return remaining_tasks

    first_task_type = first_task.get("type", "single_choice")
    first_task_id = first_task.get("id") or first_task.get("_id")

    # SEQUENTIAL ORDER: single_choice → multiple_choice → image_audio → story

    # 1. Add remaining single choice tasks (skip first one if it was single choice)
    if first_task_type == "single_choice":
        remaining_single_choice = [
            task for task in task_categories["single_choice"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_single_choice)
    else:
        remaining_tasks.extend(task_categories["single_choice"])

    # 2. Add remaining multiple choice tasks (skip first one if it was multiple choice)
    if first_task_type == "multiple_choice":
        remaining_multiple_choice = [
            task for task in task_categories["multiple_choice"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_multiple_choice)
    else:
        remaining_tasks.extend(task_categories["multiple_choice"])

    # 3. Add remaining image/audio tasks (skip first one if it was image/audio)
    if first_task_type in ["image", "audio", "image_audio", "image_identification", "speak_word"]:
        remaining_image_audio = [
            task for task in task_categories["image_audio"]
            if (task.get("id") or task.get("_id")) != first_task_id
        ]
        remaining_tasks.extend(remaining_image_audio)
    else:
        remaining_tasks.extend(task_categories["image_audio"])

    # 4. Add all story tasks (stories are always processed last in background)
    remaining_tasks.extend(task_categories["story"])

    logger.info(f"📋 REMAINING TASKS (sequential order): {len(remaining_tasks)} tasks")
    logger.info(f"   - Single choice: {len([t for t in remaining_tasks if t.get('type') == 'single_choice'])}")
    logger.info(f"   - Multiple choice: {len([t for t in remaining_tasks if t.get('type') == 'multiple_choice'])}")
    logger.info(f"   - Image/audio: {len([t for t in remaining_tasks if t.get('type') in ['image', 'audio', 'image_audio', 'image_identification', 'speak_word']])}")
    logger.info(f"   - Stories: {len([t for t in remaining_tasks if t.get('type') == 'story'])}")

    return remaining_tasks


async def _save_tasks_to_database(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None
) -> Optional[str]:
    """Save tasks to database and return collection_id."""
    try:
        tasks = tasks_data.get("tasks", [])
        if not tasks:
            return None

        # Create collection if not provided
        if not collection_id:
            collection_id = ObjectId()

        # Use title from Gemini response or fallback to session-based title
        gemini_title = tasks_data.get("title", f"Task Set {session_id[:8]}")

        # Extract thumbnail from Gemini response if available
        gemini_thumbnail = tasks_data.get("thumbnail", gemini_title)  # Use title as fallback

        # Prepare task set document
        task_set_doc = {
            "_id": collection_id,
            "session_id": session_id,
            "user_id": ObjectId(current_user.user.id),
            "title": gemini_title,  # Use title from Gemini
            "thumbnail": gemini_thumbnail,  # Add thumbnail field
            "difficulty_level": "medium",
            "input_content": audio_storage_info if audio_storage_info else None,
            "input_type": InputType.AUDIO.value,
            "gentype": GenerationType.PRIMARY.value,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "tasks": [],  # List of string IDs
            "stories": [],  # List of string IDs
            "followup_count": 0,
            "total_tasks": len(tasks),
            "total_stories": 0,  # Will be updated when stories are processed
            "attempted_tasks": 0,
            "total_score": len(tasks) * 10,  # 10 points per task
            "scored": 0,
            "usage": _serialize_usage_metadata(tasks_data.get("usage_metadata", {}))
        }

        # Prepare task items
        task_items = []
        for i, task in enumerate(tasks):
            task_id = ObjectId()

            # Map task type to database enum
            task_type = _map_task_type(task.get("type", "single_choice"))

            # Standardize correct_answer format to match editor service and followup service
            question_data = task.get("question", {})
            raw_answer = question_data.get("answer", "")

            # Build standardized correct answer structure with proper value types
            if task.get("type") in ["single_choice", "speak_word", "image_identification", "word_identification"]:
                correct_answer_data = {
                    "type": "single",
                    "value": raw_answer  # Single value as string (e.g., "a")
                }
            else:  # multiple_choice
                # Handle multiple choice answers - convert string to array if needed
                if isinstance(raw_answer, str):
                    # If Gemini returns "a,b" convert to ["a", "b"]
                    if "," in raw_answer:
                        answer_array = [item.strip() for item in raw_answer.split(",")]
                    else:
                        # If single answer like "a", convert to ["a"]
                        answer_array = [raw_answer.strip()]
                else:
                    # If already an array, use as-is
                    answer_array = raw_answer if isinstance(raw_answer, list) else [str(raw_answer)]

                correct_answer_data = {
                    "type": "multiple",
                    "value": answer_array  # Array of values (e.g., ["a", "b"])
                }

            # Standardize question format to match all services
            standardized_question = {
                "type": task.get("type", "single_choice"),
                "text": question_data.get("text", ""),
                "translated_text": question_data.get("translated_text", ""),
                "options": question_data.get("options", {}),
                "answer_hint": question_data.get("answer_hint", ""),
                "options_metadata": {},  # Will be populated during options audio generation
                "image_metadata": {},    # Will be populated during image generation
                "audio_metadata": {},    # Will be populated during audio generation
                "metadata": {
                }
            }

            task_item = {
                "_id": task_id,
                "task_set_id": collection_id,
                "user_id": ObjectId(current_user.user.id),
                "question": standardized_question,
                "correct_answer": correct_answer_data,  # Use standardized format
                "user_answer": None,
                "story": task.get("story", {}),
                "type": task_type,
                "input_type": InputType.AUDIO.value,
                "status": TaskStatus.PENDING.value,
                "result": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "image_metadata": {},  # Top level for image tasks
                "audio_metadata": {},  # Top level for audio tasks
                "metadata": {
                    "_priority": i,
                    "_media_ready": False,
                    "_options_audio_ready": False,
                    "status": "generating"  # Universal root status - default to generating
                }
            }

            task_items.append(task_item)
            task_set_doc["tasks"].append(str(task_id))  # Convert ObjectId to string

            # Add task_id to original task for processing
            task["id"] = task_id

        # Save to database
        await current_user.async_db.task_sets.insert_one(task_set_doc)
        await current_user.async_db.task_items.insert_many(task_items)

        logger.info(f"✅ Saved {len(task_items)} tasks to database with collection_id: {collection_id}")
        return str(collection_id)

    except Exception as e:
        logger.error(f"❌ Failed to save tasks to database: {e}")
        return None


def _map_task_type(prompt_maker_type: str) -> str:
    """Map prompt_maker.py task types to database QuizType enum values."""
    type_mapping = {
        "single_choice": QuizType.SINGLE_CHOICE.value,
        "multiple_choice": QuizType.MULTIPLE_CHOICE.value,
        "image_identification": QuizType.IMAGE_IDENTIFICATION.value,
        "image_identify": QuizType.IMAGE_IDENTIFICATION.value,
        "speak_word": QuizType.SPEAK_WORD.value,
        "true_false": QuizType.SINGLE_CHOICE.value,
        "fill_in_blank": QuizType.ANSWER_IN_WORD.value,
        "visual_question": QuizType.IMAGE_IDENTIFICATION.value,
        "pronunciation": QuizType.SPEAK_WORD.value,
        "audio_identification": QuizType.SPEAK_WORD.value,
    }
    return type_mapping.get(prompt_maker_type, QuizType.SINGLE_CHOICE.value)


def _serialize_usage_metadata(usage_metadata: Any) -> Dict[str, Any]:
    """Convert usage metadata to a MongoDB-serializable dictionary."""
    if not usage_metadata:
        return {}

    try:
        if hasattr(usage_metadata, 'model_dump'):
            return usage_metadata.model_dump()
        elif hasattr(usage_metadata, 'dict'):
            return usage_metadata.dict()
        elif isinstance(usage_metadata, dict):
            return usage_metadata
        else:
            return dict(usage_metadata) if usage_metadata else {}
    except Exception as e:
        logger.warning(f"Failed to serialize usage metadata: {e}")
        return {"serialization_error": str(e)}


async def _process_stories(
    current_user: UserTenantDB,
    session_id: str,
    stories: List[Dict[str, Any]],
    task_collection_id: str,
    audio_storage_info: Optional[Dict[str, Any]] = None
) -> Optional[str]:
    """Process stories and save to database (PRESERVED LOGIC)."""
    try:
        if not stories:
            return None

        logger.info(f"📚 Processing {len(stories)} stories for collection {task_collection_id}")

        # Create story collection
        story_collection_id = ObjectId()

        story_collection_doc = {
            "_id": story_collection_id,
            "session_id": session_id,
            "user_id": ObjectId(current_user.user.id),
            "task_collection_id": ObjectId(task_collection_id),
            "title": f"Stories {session_id[:8]}",
            "input_content": audio_storage_info if audio_storage_info else None,
            "input_type": InputType.AUDIO.value,
            "status": TaskStatus.PENDING.value,
            "total_stories": len(stories),
            "completed_stories": 0,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "stories": []
        }

        # Create story items
        story_items = []
        for i, story in enumerate(stories):
            story_id = ObjectId()

            story_item = {
                "_id": story_id,
                "story_collection_id": story_collection_id,
                "user_id": ObjectId(current_user.user.id),
                "stage": i + 1,
                "script": story.get("script", ""),
                "image": story.get("image", ""),
                "thumbnail": story.get("thumbnail", "📖"),
                "status": TaskStatus.PENDING.value,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "audio_metadata": {},
                "image_metadata": {},
                "metadata": {
                    "_priority": i,
                    "_media_ready": False
                }
            }

            story_items.append(story_item)
            story_collection_doc["stories"].append(story_id)

        # Save to database
        # await current_user.async_db.story_collections.insert_one(story_collection_doc)
        await current_user.async_db.story_steps.insert_many(story_items)

        # Update task set with story IDs (same as tasks field) - convert to strings
        story_ids = [str(story_item["_id"]) for story_item in story_items]
        await current_user.async_db.task_sets.update_one(
            {"_id": ObjectId(task_collection_id)},
            {
                "$set": {
                    "stories": story_ids,
                    "total_stories": len(story_ids),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Generate story media in background if enabled
        if _is_story_image_enabled(current_user) or _is_story_audio_enabled(current_user):
            for story_item in story_items:
                asyncio.create_task(_generate_story_media(
                    current_user, story_item, story_item["_id"]
                ))
            logger.info(f"🎬 Scheduled story media generation for {len(story_items)} stories")

        logger.info(f"✅ Saved {len(story_items)} stories to database with collection_id: {story_collection_id}")
        logger.info(f"✅ Updated task set {task_collection_id} with {len(story_ids)} story IDs")
        return str(story_collection_id)

    except Exception as e:
        logger.error(f"❌ Failed to process stories: {e}")
        return None


async def _process_task_by_type(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Process a task based on its type (PRESERVED LOGIC)."""
    task_type = task.get("type", "single_choice")
    task_id = task.get("id") or task.get("_id")

    logger.info(f"🔄 Processing task {task_id} of type: {task_type}")

    try:
        if task_type == "single_choice":
            await _process_single_choice_task_complete(current_user, task, socketio_server)
        elif task_type == "multiple_choice":
            await _process_multiple_choice_task_complete(current_user, task, socketio_server)
        elif task_type in ["image", "audio", "image_audio", "image_identification", "speak_word"]:
            await _process_image_audio_task_complete(current_user, task, socketio_server)
        elif task_type == "story":
            await _process_story_task_complete(current_user, task, socketio_server)
        else:
            # Default to single choice for unknown types
            logger.warning(f"Unknown task type '{task_type}' for task {task_id}, processing as single choice")
            await _process_single_choice_task_complete(current_user, task, socketio_server)

    except Exception as e:
        logger.error(f"❌ Failed to process task {task_id} of type {task_type}: {e}")
        raise


async def _process_remaining_tasks_sequentially(
    current_user: UserTenantDB,
    remaining_tasks: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """Process remaining tasks sequentially in background (PRESERVED LOGIC)."""
    logger.info(f"🔄 Processing {len(remaining_tasks)} remaining tasks sequentially in background")

    for i, task in enumerate(remaining_tasks):
        try:
            task_type = task.get("type", "single_choice")
            task_id = task.get("id") or task.get("_id")
            logger.info(f"🔄 Background task {i+1}/{len(remaining_tasks)}: {task_type} (ID: {task_id})")

            # Process task by type
            await _process_task_by_type(current_user, task, socketio_server)

            # Add delay between tasks to avoid rate limiting (PRESERVED LOGIC)
            if i < len(remaining_tasks) - 1:  # Don't delay after last task
                await asyncio.sleep(2)

        except Exception as e:
            logger.error(f"❌ Failed to process background task {i+1}: {e}")
            continue

    logger.info(f"✅ Completed processing {len(remaining_tasks)} background tasks sequentially")


async def _process_single_choice_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Process single choice task with options audio generation (PRESERVED LOGIC)."""
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎯 SINGLE CHOICE: Processing task {task_id} with options audio")

    try:
        # Generate options audio sequentially (PRESERVED LOGIC)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, socketio_server)

        # Mark task as completed and update database
        task["status"] = "completed"
        task["completed_at"] = datetime.now(timezone.utc).isoformat()

        # Update task in database with completion status (NO FOLLOWUP TRIGGER - only from user submission)
        await _update_task_generation_completion_status(current_user, task_id, "single_choice")

        logger.info(f"✅ Single choice task {task_id} completed with options audio")

    except Exception as e:
        logger.error(f"❌ Failed to process single choice task {task_id}: {e}")
        raise


async def _process_multiple_choice_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Process multiple choice task with options audio generation (PRESERVED LOGIC)."""
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎯 MULTIPLE CHOICE: Processing task {task_id} with options audio")

    try:
        # Generate options audio sequentially (PRESERVED LOGIC)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, socketio_server)

        # Mark task as completed and update database
        task["status"] = "completed"
        task["completed_at"] = datetime.now(timezone.utc).isoformat()

        # Update task in database with completion status (NO FOLLOWUP TRIGGER - only from user submission)
        await _update_task_generation_completion_status(current_user, task_id, "multiple_choice")

        logger.info(f"✅ Multiple choice task {task_id} completed with options audio")

    except Exception as e:
        logger.error(f"❌ Failed to process multiple choice task {task_id}: {e}")
        raise


async def _process_image_audio_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Process image/audio task with media and options audio generation (PRESERVED LOGIC)."""
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎯 IMAGE/AUDIO: Processing task {task_id} with media and options audio")

    try:
        # Generate task media (image/audio) first (PRESERVED LOGIC)
        await _generate_task_media(current_user, task, task_id, socketio_server)

        # Generate options audio sequentially (PRESERVED LOGIC)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, socketio_server)

        # Mark task as completed and update database
        task["status"] = "completed"
        task["completed_at"] = datetime.now(timezone.utc).isoformat()

        # Update task in database with completion status (NO FOLLOWUP TRIGGER - only from user submission)
        await _update_task_generation_completion_status(current_user, task_id, task.get("type", "image_audio"))

        logger.info(f"✅ Image/audio task {task_id} completed with media and options audio")

    except Exception as e:
        logger.error(f"❌ Failed to process image/audio task {task_id}: {e}")
        raise


async def _process_story_task_complete(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Process story task (PRESERVED LOGIC)."""
    task_id = task.get("id") or task.get("_id")
    logger.info(f"🎯 STORY: Processing task {task_id}")

    try:
        # Story processing logic would go here
        # For now, just mark as completed
        task["status"] = "completed"
        task["completed_at"] = datetime.now(timezone.utc).isoformat()

        logger.info(f"✅ Story task {task_id} completed")

    except Exception as e:
        logger.error(f"❌ Failed to process story task {task_id}: {e}")
        raise


async def _generate_options_audio_sequential(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate audio for all task options SEQUENTIALLY to avoid rate limits (PRESERVED LOGIC)."""
    try:
        # Set status to generating at start
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "generating",
                    "metadata.status": "generating",  # Universal root status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Check if options audio is enabled globally (simplified config check)
        if not _is_options_audio_enabled(current_user):
            logger.info(f"⏭️  TASK {task_id} | OPTIONS AUDIO: DISABLED GLOBALLY")
            # Set status to completed even if disabled
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.options_audio_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            return

        question = task.get("question", {})
        options = question.get("options", {})

        if not options:
            logger.debug(f"📝 Task {task_id} has no options, skipping options audio generation")
            # Set status to completed if no options
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.options_audio_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            return

        logger.info(f"🔊 SEQUENTIAL: Generating audio for {len(options)} options in task {task_id}")

        options_metadata = {}
        total_options = len(options)

        # Process each option sequentially (PRESERVED LOGIC)
        for i, (option_key, option_value) in enumerate(options.items(), 1):
            try:
                logger.info(f"🎵 OPTION {i}/{total_options} | TASK {task_id} | PROCESSING: {option_key}")

                # Generate audio for this option
                audio_url, file_info = await _generate_single_option_audio(
                    current_user, str(option_value), task_id, option_key, i, total_options
                )

                options_metadata[option_key] = {
                    "text": str(option_value),
                    "audio_url": audio_url,
                    "file_info": file_info,  # Store complete file info for URL refresh
                    "cache_id": f"{str(option_value).strip().lower()}_audio",  # Media cache ID
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

                # Add delay between options to avoid rate limits (PRESERVED LOGIC)
                if i < total_options:
                    await asyncio.sleep(2)  # 2 second delay between options

            except Exception as option_error:
                logger.error(f"❌ Failed to generate audio for option {option_key}: {option_error}")
                options_metadata[option_key] = {
                    "text": str(option_value),
                    "audio_url": None,
                    "file_info": None,  # No file info on error
                    "cache_id": f"{str(option_value).strip().lower()}_audio",  # Media cache ID
                    "error": str(option_error),
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
                continue

        # Update task with options metadata
        if isinstance(task_id, str):
            task_id = ObjectId(task_id)

        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "question.options_metadata": options_metadata,
                    "metadata._options_audio_ready": True,
                    "metadata.options_audio_status": "completed",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Update overall media status after options audio completion
        await _update_overall_media_status(current_user, task_id)

        logger.info(f"✅ TASK {task_id} | OPTIONS AUDIO: Generated for {len(options_metadata)} options")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "failed",
                    "metadata.options_audio_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Update overall media status after options audio failure
        await _update_overall_media_status(current_user, task_id)

        logger.error(f"❌ Failed to generate options audio for task {task_id}: {e}")


async def _generate_single_option_audio(
    current_user: UserTenantDB,
    option_text: str,
    task_id: ObjectId,
    option_key: str,
    option_number: int,
    total_options: int
) -> tuple[Optional[str], Optional[dict]]:
    """Generate audio for a single option with caching (PRESERVED LOGIC).

    Returns:
        tuple: (audio_url, file_info) where file_info contains complete MinIO metadata
    """
    try:
        # Check cache first (simplified cache check)
        cached_audio = await _check_media_cache(current_user, option_text, "audio")

        if cached_audio:
            audio_url = cached_audio.get("url")
            file_info = cached_audio.get("file_info", {})
            logger.info(f"🎯 OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: CACHED | URL: {audio_url}")
            return audio_url, file_info

        # Generate new audio
        logger.info(f"🎵 OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: GENERATING | TEXT: {option_text}")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, option_text, "audio_prompt")

        if file_info and file_info.get("url"):
            audio_url = file_info.get("url")

            # Save to cache for future use (simplified cache save)
            await _save_media_cache(current_user, option_text, "audio", file_info)

            logger.info(f"✅ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: GENERATED | URL: {audio_url}")
            return audio_url, file_info
        else:
            logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: NO_DATA")
            return None, None

    except Exception as e:
        logger.error(f"❌ Failed to generate audio for option {option_key}: {e}")
        return None


async def _generate_task_media(
    current_user: UserTenantDB,
    task: Dict[str, Any],
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate task media (image/audio) (PRESERVED LOGIC)."""
    try:
        # Get keyword from task
        keyword = task.get("question", {}).get("answer_hint", "")
        if not keyword:
            logger.warning(f"No keyword found for task {task_id}")
            return

        # Generate image if needed
        if _is_quiz_image_enabled(current_user):
            await _generate_and_store_task_image(current_user, keyword, task_id)

        # Generate audio if needed
        if _is_quiz_audio_enabled(current_user):
            await _generate_and_store_task_audio(current_user, keyword, task_id)

    except Exception as e:
        logger.error(f"❌ Failed to generate task media for {task_id}: {e}")


# ============================================================================
# SIMPLIFIED CONFIGURATION FUNCTIONS (5 GLOBAL SWITCHES)
# ============================================================================

def _is_options_audio_enabled(current_user: UserTenantDB) -> bool:
    """Check if options audio is enabled globally (simplified config)."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("options_audio_enabled", True) if config_doc else True
    except:
        return True  # Default to enabled


def _is_quiz_image_enabled(current_user: UserTenantDB) -> bool:
    """Check if quiz image is enabled globally (simplified config)."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("quiz_image_enabled", True) if config_doc else True
    except:
        return True  # Default to enabled


def _is_quiz_audio_enabled(current_user: UserTenantDB) -> bool:
    """Check if quiz audio is enabled globally (simplified config)."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("quiz_audio_enabled", True) if config_doc else True
    except:
        return True  # Default to enabled


def _is_story_image_enabled(current_user: UserTenantDB) -> bool:
    """Check if story image is enabled globally (simplified config)."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("story_image_enabled", True) if config_doc else True
    except:
        return True  # Default to enabled


def _is_story_audio_enabled(current_user: UserTenantDB) -> bool:
    """Check if story audio is enabled globally (simplified config)."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("story_audio_enabled", True) if config_doc else True
    except:
        return True  # Default to enabled


# ============================================================================
# SIMPLIFIED CACHE AND MEDIA FUNCTIONS
# ============================================================================

async def _check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str) -> Optional[Dict[str, Any]]:
    """
    Check media cache with multi-user support.

    Returns cached media if available, regardless of which user originally created it.
    Updates access tracking for the current user.
    """
    try:
        cached_item = await current_user.async_db.media.find_one({
            "keyword": keyword.strip().lower(),
            "media_type": media_type
        })

        if cached_item and cached_item.get("file_info"):
            # Update user access tracking
            user_ids = cached_item.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)
                await current_user.async_db.media.update_one(
                    {"_id": cached_item["_id"]},
                    {
                        "$set": {
                            "user_ids": user_ids,
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )
            else:
                # Just update last access time
                await current_user.async_db.media.update_one(
                    {"_id": cached_item["_id"]},
                    {
                        "$set": {
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )

            # Generate fresh presigned URL for cached media
            try:
                file_info = cached_item["file_info"].copy()
                if file_info.get("object_name"):
                    presigned_url = current_user.minio.get_presigned_url(
                        bucket_name=current_user.minio_bucket_name,
                        object_name=file_info["object_name"],
                        expires=timedelta(hours=24),
                        method="GET"
                    )
                    file_info["url"] = presigned_url
                return file_info
            except Exception as url_error:
                logger.warning(f"Failed to generate presigned URL for cached media: {url_error}")
                return None
        return None
    except Exception as e:
        logger.warning(f"Cache check failed: {e}")
        return None


async def _update_overall_media_status(current_user: UserTenantDB, task_id: ObjectId):
    """
    Update the universal root-level status based on individual media statuses.

    Status logic:
    - "generating" if any media is still generating
    - "failed" if any media failed (with error message)
    - "completed" if all enabled media is completed
    """
    try:
        # Get current task metadata
        task_item = await current_user.async_db.task_items.find_one(
            {"_id": task_id},
            {"metadata": 1}
        )

        if not task_item:
            return

        metadata = task_item.get("metadata", {})

        # Check individual media statuses
        image_status = metadata.get("image_status")
        audio_status = metadata.get("audio_status")
        options_audio_status = metadata.get("options_audio_status")

        # Collect all statuses and errors
        statuses = []
        errors = []

        if image_status:
            statuses.append(image_status)
            if image_status == "failed":
                errors.append(f"Image: {metadata.get('image_error', 'Unknown error')}")

        if audio_status:
            statuses.append(audio_status)
            if audio_status == "failed":
                errors.append(f"Audio: {metadata.get('audio_error', 'Unknown error')}")

        if options_audio_status:
            statuses.append(options_audio_status)
            if options_audio_status == "failed":
                errors.append(f"Options Audio: {metadata.get('options_audio_error', 'Unknown error')}")

        # Determine overall status
        overall_status = "completed"  # Default to completed
        overall_error = None

        if "generating" in statuses:
            overall_status = "generating"
        elif "failed" in statuses:
            overall_status = "failed"
            overall_error = "; ".join(errors)

        # Update universal root status
        update_data = {
            "metadata.status": overall_status,  # Universal root status
            "updated_at": datetime.now(timezone.utc)
        }

        if overall_error:
            update_data["metadata.error"] = overall_error  # Universal root error
            # Use only $set when we have an error
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {"$set": update_data}
            )
        else:
            # Use both $set and $unset when removing error field
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": update_data,
                    "$unset": {"metadata.error": ""}
                }
            )

        logger.debug(f"📊 TASK {task_id} | UNIVERSAL STATUS: {overall_status}")

    except Exception as e:
        logger.error(f"❌ Failed to update universal status for task {task_id}: {e}")


async def _update_overall_story_media_status(current_user: UserTenantDB, story_id: ObjectId):
    """
    Update the universal root-level status for story media based on individual media statuses.

    Status logic:
    - "generating" if any media is still generating
    - "failed" if any media failed (with error message)
    - "completed" if all enabled media is completed
    """
    try:
        # Get current story metadata
        story_item = await current_user.async_db.story_steps.find_one(
            {"_id": story_id},
            {"metadata": 1}
        )

        if not story_item:
            return

        metadata = story_item.get("metadata", {})

        # Check individual media statuses
        image_status = metadata.get("image_status")
        audio_status = metadata.get("audio_status")

        # Collect all statuses and errors
        statuses = []
        errors = []

        if image_status:
            statuses.append(image_status)
            if image_status == "failed":
                errors.append(f"Image: {metadata.get('image_error', 'Unknown error')}")

        if audio_status:
            statuses.append(audio_status)
            if audio_status == "failed":
                errors.append(f"Audio: {metadata.get('audio_error', 'Unknown error')}")

        # Determine overall status
        overall_status = "completed"  # Default to completed
        overall_error = None

        if "generating" in statuses:
            overall_status = "generating"
        elif "failed" in statuses:
            overall_status = "failed"
            overall_error = "; ".join(errors)

        # Update universal root status
        update_data = {
            "metadata.status": overall_status,  # Universal root status
            "status": overall_status,  # Also update top-level status for stories
            "updated_at": datetime.now(timezone.utc)
        }

        if overall_error:
            update_data["metadata.error"] = overall_error  # Universal root error
            # Use only $set when we have an error
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {"$set": update_data}
            )
        else:
            # Use both $set and $unset when removing error field
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": update_data,
                    "$unset": {"metadata.error": ""}
                }
            )

        logger.debug(f"📊 STORY {story_id} | UNIVERSAL STATUS: {overall_status}")

        # Check if all tasks and stories in the task set are completed for thumbnail generation
        if overall_status == "completed":
            await _check_and_generate_thumbnail_from_story(current_user, story_id)

    except Exception as e:
        logger.error(f"❌ Failed to update universal status for story {story_id}: {e}")


async def _save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, file_info: Dict[str, Any]):
    """
    Simplified media cache save with multi-user support.

    Stores user_ids as a list to allow multiple users to access the same cached media.
    """
    try:
        # Check if media already exists
        existing_media = await current_user.async_db.media.find_one({
            "keyword": keyword.strip().lower(),
            "media_type": media_type
        })

        if existing_media:
            # Add current user to user_ids list if not already present
            user_ids = existing_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)

            await current_user.async_db.media.update_one(
                {"keyword": keyword.strip().lower(), "media_type": media_type},
                {
                    "$set": {
                        "user_ids": user_ids,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                }
            )
            logger.debug(f"💾 Updated user access for cached {media_type} for keyword: {keyword}")
        else:
            # Create new media cache entry with user_ids list
            await current_user.async_db.media.update_one(
                {"keyword": keyword.strip().lower(), "media_type": media_type},
                {
                    "$set": {
                        "keyword": keyword.strip().lower(),
                        "media_type": media_type,
                        "file_info": file_info,
                        "object_name": file_info.get("object_name"),
                        "folder": file_info.get("folder"),
                        "content_type": file_info.get("content_type"),
                        "created_at": datetime.now(timezone.utc),
                        "user_ids": [current_user.user.id],  # Store as list for multi-user access
                        "created_by": current_user.user.id,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                },
                upsert=True
            )
            logger.debug(f"💾 Cached {media_type} for keyword: {keyword} with multi-user support")
    except Exception as e:
        logger.warning(f"Failed to save media cache: {e}")


async def _generate_and_store_task_image(current_user: UserTenantDB, keyword: str, task_id: ObjectId):
    """Generate and store task image with metadata for URL extraction."""
    try:
        # Set status to generating at start
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.image_status": "generating",
                    "metadata.status": "generating",  # Universal root status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Get task to check type
        task_item = await current_user.async_db.task_items.find_one({"_id": task_id}, {"type": 1})
        task_type = task_item.get("type") if task_item else "single_choice"

        # Check cache first
        cached_image = await _check_media_cache(current_user, keyword, "image")
        if cached_image:
            logger.info(f"🎯 TASK {task_id} | IMAGE: CACHED")

            # For image_identification tasks, save to question.metadata
            if task_type == "image_identification":
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "question.metadata": cached_image,
                            "metadata._media_ready": True,
                            "metadata.image_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status
                await _update_overall_media_status(current_user, task_id)
            else:
                # For other tasks, save to top-level image_metadata
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "image_metadata": cached_image,
                            "metadata._media_ready": True,
                            "metadata.image_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status
                await _update_overall_media_status(current_user, task_id)
            return

        # Generate new image
        logger.info(f"🎨 TASK {task_id} | IMAGE: GENERATING")
        _file_text, file_info, usage_metadata = await generate_image(current_user, keyword)

        if file_info:
            await _save_media_cache(current_user, keyword, "image", file_info)

            # For image_identification tasks, save to question.metadata
            if task_type == "image_identification":
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "question.metadata": file_info,
                            "metadata._media_ready": True,
                            "metadata.image_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status
                await _update_overall_media_status(current_user, task_id)
            else:
                # For other tasks, save to top-level image_metadata
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "image_metadata": file_info,
                            "metadata._media_ready": True,
                            "metadata.image_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status
                await _update_overall_media_status(current_user, task_id)
            logger.info(f"✅ TASK {task_id} | IMAGE: GENERATED")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.image_status": "failed",
                    "metadata.image_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        # Update overall media status
        await _update_overall_media_status(current_user, task_id)
        logger.error(f"❌ Failed to generate image for task {task_id}: {e}")


async def _generate_and_store_task_audio(current_user: UserTenantDB, keyword: str, task_id: ObjectId):
    """Generate and store task audio with metadata for URL extraction."""
    try:
        # Set status to generating at start
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.audio_status": "generating",
                    "metadata.status": "generating",  # Universal root status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Get task to check type
        task_item = await current_user.async_db.task_items.find_one({"_id": task_id}, {"type": 1})
        task_type = task_item.get("type") if task_item else "single_choice"

        # Check cache first
        cached_audio = await _check_media_cache(current_user, keyword, "audio")
        if cached_audio:
            logger.info(f"🎯 TASK {task_id} | AUDIO: CACHED")

            # For speak_word tasks, save to question.metadata
            if task_type == "speak_word":
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "question.metadata": cached_audio,
                            "metadata._media_ready": True,
                            "metadata.audio_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status after cached audio completion
                await _update_overall_media_status(current_user, task_id)
            else:
                # For other tasks, save to top-level audio_metadata
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "audio_metadata": cached_audio,
                            "metadata._media_ready": True,
                            "metadata.audio_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            # Update overall media status after cached audio completion
            await _update_overall_media_status(current_user, task_id)
            return

        # Generate new audio
        logger.info(f"🔊 TASK {task_id} | AUDIO: GENERATING")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, keyword, "audio_prompt")

        if file_info:
            await _save_media_cache(current_user, keyword, "audio", file_info)

            # For speak_word tasks, save to question.metadata
            if task_type == "speak_word":
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "question.metadata": file_info,
                            "metadata._media_ready": True,
                            "metadata.audio_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                # Update overall media status after audio completion
                await _update_overall_media_status(current_user, task_id)
            else:
                # For other tasks, save to top-level audio_metadata
                await current_user.async_db.task_items.update_one(
                    {"_id": task_id},
                    {
                        "$set": {
                            "audio_metadata": file_info,
                            "metadata._media_ready": True,
                            "metadata.audio_status": "completed",
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            # Update overall media status after audio completion
            await _update_overall_media_status(current_user, task_id)
            logger.info(f"✅ TASK {task_id} | AUDIO: GENERATED")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.audio_status": "failed",
                    "metadata.audio_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Update overall media status after audio failure
        await _update_overall_media_status(current_user, task_id)
        logger.error(f"❌ Failed to generate audio for task {task_id}: {e}")


async def _generate_story_media(
    current_user: UserTenantDB,
    story: Dict[str, Any],
    story_id: ObjectId
):
    """Generate story media (image/audio) if enabled."""
    try:
        # Set initial universal status to generating
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata.status": "generating",
                    "status": "generating",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Get script text for audio generation
        script = story.get("script", "")
        # Get image text for image generation
        image_text = story.get("image", "")

        # Generate story image if enabled (using image key)
        if _is_story_image_enabled(current_user) and image_text:
            await _generate_and_store_story_image(current_user, image_text, story_id)
        elif _is_story_image_enabled(current_user) and not image_text:
            logger.warning(f"No image text found for story {story_id}")

        # Generate story audio if enabled (using script key)
        if _is_story_audio_enabled(current_user) and script:
            await _generate_and_store_story_audio(current_user, script, story_id)
        elif _is_story_audio_enabled(current_user) and not script:
            logger.warning(f"No script found for story {story_id}")

    except Exception as e:
        logger.error(f"❌ Failed to generate story media for {story_id}: {e}")


async def _generate_and_store_story_image(current_user: UserTenantDB, image_text: str, story_id: ObjectId):
    """Generate and store story image using image key text."""
    try:
        # Set status to generating at start
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata.image_status": "generating",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Check cache first
        cached_image = await _check_media_cache(current_user, image_text, "image")
        if cached_image:
            logger.info(f"🎯 STORY {story_id} | IMAGE: CACHED")

            # Save cached image to story
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "image_metadata": cached_image,
                        "metadata._media_ready": True,
                        "metadata.image_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Update overall story media status
            await _update_overall_story_media_status(current_user, story_id)
            return

        # Generate new image using image text
        logger.info(f"🎨 STORY {story_id} | IMAGE: GENERATING from image text")
        _file_text, file_info, usage_metadata = await generate_image(current_user, image_text)

        if file_info:
            await _save_media_cache(current_user, image_text, "image", file_info)

            # Save image to story
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "image_metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata.image_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Update overall story media status
            await _update_overall_story_media_status(current_user, story_id)
            logger.info(f"✅ STORY {story_id} | IMAGE: GENERATED from image text")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata.image_status": "failed",
                    "metadata.image_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Update overall story media status after failure
        await _update_overall_story_media_status(current_user, story_id)
        logger.error(f"❌ Failed to generate image for story {story_id}: {e}")


async def _generate_and_store_story_audio(current_user: UserTenantDB, script: str, story_id: ObjectId):
    """Generate and store story audio."""
    try:
        # Set status to generating at start
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata.audio_status": "generating",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Check cache first
        cached_audio = await _check_media_cache(current_user, script, "audio")
        if cached_audio:
            logger.info(f"🎯 STORY {story_id} | AUDIO: CACHED")

            # Save cached audio to story
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": cached_audio,
                        "metadata._media_ready": True,
                        "metadata.audio_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Update overall story media status
            await _update_overall_story_media_status(current_user, story_id)
            return

        # Generate new audio
        logger.info(f"🔊 STORY {story_id} | AUDIO: GENERATING")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, script, "audio_prompt")

        if file_info:
            await _save_media_cache(current_user, script, "audio", file_info)

            # Save audio to story
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata.audio_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Update overall story media status
            await _update_overall_story_media_status(current_user, story_id)
            logger.info(f"✅ STORY {story_id} | AUDIO: GENERATED")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata.audio_status": "failed",
                    "metadata.audio_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Update overall story media status after failure
        await _update_overall_story_media_status(current_user, story_id)
        logger.error(f"❌ Failed to generate audio for story {story_id}: {e}")


# FOLLOWUP TRIGGER REMOVED FROM SOCKET SERVICE V2
# Followup generation should ONLY be triggered from user task submission in Management Service V1
# NOT from task generation completion in Socket Service V2

# TASK PROGRESS TRACKING REMOVED FROM SOCKET SERVICE V2
# Task progress (attempted_tasks) should ONLY be updated from user task submission in Management Service V1
# NOT from task generation/media generation completion in Socket Service V2


# Aliases for backward compatibility
save_task_collection_and_items = save_task_collection_and_items_with_priority
save_task_collection_and_items_v2 = save_task_collection_and_items_with_priority


async def _update_task_generation_completion_status(
    current_user: UserTenantDB,
    task_id: ObjectId,
    task_type: str
):
    """Update task completion status after generation (NO FOLLOWUP TRIGGER)."""
    try:
        # Update task item status in database
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "status": TaskStatus.COMPLETED.value,
                    "metadata.status": "completed",  # Universal status for thumbnail generation
                    "metadata._task_sequential_complete": True,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ Updated generation completion status for task {task_id} ({task_type})")

        # Check if all tasks and stories in the task set are completed for thumbnail generation
        await _check_and_generate_thumbnail(current_user, task_id)

    except Exception as e:
        logger.error(f"❌ Failed to update task generation completion status: {e}")


async def _check_and_generate_thumbnail(current_user: UserTenantDB, task_id: ObjectId):
    """
    Check if all tasks and stories in a task set are completed and generate thumbnail if needed.

    Args:
        current_user: User context with database access
        task_id: The task ID that just completed
    """
    try:
        # Get the task item to find its task set
        task_item = await current_user.async_db.task_items.find_one(
            {"_id": task_id},
            {"task_set_id": 1}
        )

        if not task_item or not task_item.get("task_set_id"):
            return

        task_set_id = task_item["task_set_id"]

        # Get the task set with thumbnail info - try both collections
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"tasks": 1, "stories": 1, "thumbnail": 1, "thumbnail_metadata": 1}
        )

        # If not found in task_sets, try curated_content_set (editor service)
        if not task_set:
            task_set = await current_user.async_db.curated_content_set.find_one(
                {"_id": ObjectId(task_set_id)},
                {"tasks": 1, "stories": 1, "thumbnail": 1, "thumbnail_metadata": 1}
            )

        if not task_set:
            return

        # Skip if thumbnail already exists
        if task_set.get("thumbnail_metadata"):
            return

        # Skip if no thumbnail keyword is set
        thumbnail_keyword = task_set.get("thumbnail")
        if not thumbnail_keyword:
            return

        # Check if all tasks are completed
        all_tasks_completed = await _are_all_tasks_completed(current_user, task_set.get("tasks", []))

        # Check if all stories are completed
        all_stories_completed = await _are_all_stories_completed(current_user, task_set.get("stories", []))

        # Debug logging
        logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Thumbnail keyword: {thumbnail_keyword}")
        logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Tasks: {len(task_set.get('tasks', []))} | Stories: {len(task_set.get('stories', []))}")
        logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Tasks completed: {all_tasks_completed} | Stories completed: {all_stories_completed}")
        logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Task IDs: {task_set.get('tasks', [])}")
        logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Story IDs: {task_set.get('stories', [])}")

        # Generate thumbnail if all tasks and stories are completed
        if all_tasks_completed and all_stories_completed:
            logger.info(f"🖼️ All tasks and stories completed for task set {task_set_id}. Generating thumbnail...")
            await _generate_and_store_thumbnail(current_user, task_set_id, thumbnail_keyword)
        else:
            logger.info(f"🔍 THUMBNAIL CHECK | TASK SET {task_set_id} | Not ready - Tasks: {all_tasks_completed}, Stories: {all_stories_completed}")

    except Exception as e:
        logger.error(f"❌ Failed to check and generate thumbnail: {e}")


async def _are_all_tasks_completed(current_user: UserTenantDB, task_ids: List[str]) -> bool:
    """
    Check if all tasks in the list are completed.

    Args:
        current_user: User context with database access
        task_ids: List of task IDs to check

    Returns:
        True if all tasks are completed, False otherwise
    """
    try:
        if not task_ids:
            return True

        # Convert string IDs to ObjectIds
        object_ids = [ObjectId(task_id) for task_id in task_ids]

        # Count total tasks
        total_tasks = len(object_ids)

        # Count completed tasks from both collections (tasks can be in either collection)
        completed_tasks_primary = await current_user.async_db.task_items.count_documents({
            "_id": {"$in": object_ids},
            "metadata.status": "completed"
        })

        completed_tasks_editor = await current_user.async_db.curated_content_items.count_documents({
            "_id": {"$in": object_ids},
            "metadata.status": "completed"
        })

        completed_tasks = completed_tasks_primary + completed_tasks_editor

        logger.info(f"🔍 TASKS COMPLETION CHECK: {completed_tasks}/{total_tasks} completed")

        # Debug: Check individual task statuses
        for task_id in task_ids:
            task_status = await current_user.async_db.task_items.find_one(
                {"_id": ObjectId(task_id)},
                {"metadata.status": 1, "status": 1}
            )
            logger.info(f"🔍 TASK {task_id} STATUS: {task_status}")
        return completed_tasks == total_tasks

    except Exception as e:
        logger.error(f"❌ Failed to check tasks completion: {e}")
        return False


async def _are_all_stories_completed(current_user: UserTenantDB, story_ids: List[str]) -> bool:
    """
    Check if all stories in the list are completed.

    Args:
        current_user: User context with database access
        story_ids: List of story IDs to check

    Returns:
        True if all stories are completed, False otherwise
    """
    try:
        if not story_ids:
            return True

        # Convert string IDs to ObjectIds
        object_ids = [ObjectId(story_id) for story_id in story_ids]

        # Count total stories
        total_stories = len(object_ids)

        # Count completed stories from both collections (stories can be in either collection)
        completed_stories_primary = await current_user.async_db.story_steps.count_documents({
            "_id": {"$in": object_ids},
            "metadata.status": "completed"
        })

        completed_stories_editor = await current_user.async_db.curated_content_story.count_documents({
            "_id": {"$in": object_ids},
            "metadata.status": "completed"
        })

        completed_stories = completed_stories_primary + completed_stories_editor

        logger.debug(f"Stories completion check: {completed_stories}/{total_stories} completed")
        return completed_stories == total_stories

    except Exception as e:
        logger.error(f"❌ Failed to check stories completion: {e}")
        return False


async def _generate_and_store_thumbnail(current_user: UserTenantDB, task_set_id: str, thumbnail_keyword: str):
    """
    Generate and store thumbnail image for a task set with permanent URL.

    Args:
        current_user: User context with database access
        task_set_id: The task set ID
        thumbnail_keyword: The keyword to use for thumbnail generation
    """
    try:
        logger.info(f"🖼️ THUMBNAIL | TASK SET {task_set_id} | GENERATING with keyword: {thumbnail_keyword}")

        # Check cache first
        cached_thumbnail = await _check_media_cache(current_user, thumbnail_keyword, "image")
        if cached_thumbnail:
            logger.info(f"🎯 THUMBNAIL | TASK SET {task_set_id} | CACHED")

            # Generate permanent URL (365 days)
            permanent_url = current_user.minio.get_presigned_url(
                bucket_name=current_user.minio_bucket_name,
                object_name=cached_thumbnail["object_name"],
                expires=timedelta(days=7),
                method="GET"
            )

            # Update cached thumbnail with permanent URL
            thumbnail_metadata = cached_thumbnail.copy()
            thumbnail_metadata["url"] = permanent_url

            # Save thumbnail metadata to task set - try both collections
            update_result = await current_user.async_db.task_sets.update_one(
                {"_id": ObjectId(task_set_id)},
                {
                    "$set": {
                        "thumbnail_metadata": thumbnail_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # If not updated in task_sets, try curated_content_set (editor service)
            if update_result.matched_count == 0:
                await current_user.async_db.curated_content_set.update_one(
                    {"_id": ObjectId(task_set_id)},
                    {
                        "$set": {
                            "thumbnail_metadata": thumbnail_metadata,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            logger.info(f"✅ THUMBNAIL | TASK SET {task_set_id} | CACHED thumbnail saved with permanent URL")
            return

        # Generate new thumbnail image
        logger.info(f"🎨 THUMBNAIL | TASK SET {task_set_id} | GENERATING new image")
        _file_text, file_info, usage_metadata = await generate_image(current_user, thumbnail_keyword)

        if file_info:
            # Cache the generated thumbnail
            await _save_media_cache(current_user, thumbnail_keyword, "image", file_info)

            # Generate permanent URL (365 days)
            permanent_url = current_user.minio.get_presigned_url(
                bucket_name=current_user.minio_bucket_name,
                object_name=file_info["object_name"],
                expires=timedelta(days=7),
                method="GET"
            )

            # Update file_info with permanent URL
            thumbnail_metadata = file_info.copy()
            thumbnail_metadata["url"] = permanent_url

            # Save thumbnail metadata to task set - try both collections
            update_result = await current_user.async_db.task_sets.update_one(
                {"_id": ObjectId(task_set_id)},
                {
                    "$set": {
                        "thumbnail_metadata": thumbnail_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # If not updated in task_sets, try curated_content_set (editor service)
            if update_result.matched_count == 0:
                await current_user.async_db.curated_content_set.update_one(
                    {"_id": ObjectId(task_set_id)},
                    {
                        "$set": {
                            "thumbnail_metadata": thumbnail_metadata,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            logger.info(f"✅ THUMBNAIL | TASK SET {task_set_id} | Generated and saved with permanent URL")
        else:
            logger.error(f"❌ THUMBNAIL | TASK SET {task_set_id} | Failed to generate image")

    except Exception as e:
        logger.error(f"❌ Failed to generate thumbnail for task set {task_set_id}: {e}")


async def _check_and_generate_thumbnail_from_story(current_user: UserTenantDB, story_id: ObjectId):
    """
    Check if all tasks and stories in a task set are completed and generate thumbnail if needed.
    This is called when a story completes.

    Args:
        current_user: User context with database access
        story_id: The story ID that just completed
    """
    try:
        # Get the story to find its task set
        story_item = await current_user.async_db.story_steps.find_one(
            {"_id": story_id},
            {"task_set_id": 1}
        )

        if not story_item or not story_item.get("task_set_id"):
            return

        task_set_id = story_item["task_set_id"]

        # Get the task set with thumbnail info - try both collections
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"tasks": 1, "stories": 1, "thumbnail": 1, "thumbnail_metadata": 1}
        )

        # If not found in task_sets, try curated_content_set (editor service)
        if not task_set:
            task_set = await current_user.async_db.curated_content_set.find_one(
                {"_id": ObjectId(task_set_id)},
                {"tasks": 1, "stories": 1, "thumbnail": 1, "thumbnail_metadata": 1}
            )

        if not task_set:
            return

        # Skip if thumbnail already exists
        if task_set.get("thumbnail_metadata"):
            return

        # Skip if no thumbnail keyword is set
        thumbnail_keyword = task_set.get("thumbnail")
        if not thumbnail_keyword:
            return

        # Check if all tasks are completed
        all_tasks_completed = await _are_all_tasks_completed(current_user, task_set.get("tasks", []))

        # Check if all stories are completed
        all_stories_completed = await _are_all_stories_completed(current_user, task_set.get("stories", []))

        # Debug logging
        logger.info(f"🔍 THUMBNAIL CHECK FROM STORY | TASK SET {task_set_id} | Tasks: {len(task_set.get('tasks', []))} | Stories: {len(task_set.get('stories', []))}")
        logger.info(f"🔍 THUMBNAIL CHECK FROM STORY | TASK SET {task_set_id} | Tasks completed: {all_tasks_completed} | Stories completed: {all_stories_completed}")

        # Generate thumbnail if all tasks and stories are completed
        if all_tasks_completed and all_stories_completed:
            logger.info(f"🖼️ All tasks and stories completed for task set {task_set_id}. Generating thumbnail...")
            await _generate_and_store_thumbnail(current_user, task_set_id, thumbnail_keyword)
        else:
            logger.info(f"🔍 THUMBNAIL CHECK FROM STORY | TASK SET {task_set_id} | Not ready - Tasks: {all_tasks_completed}, Stories: {all_stories_completed}")

    except Exception as e:
        logger.error(f"❌ Failed to check and generate thumbnail from story: {e}")


# FOLLOWUP TRIGGER REMOVED FROM SOCKET SERVICE V2
# Followup generation should ONLY be triggered from user task submission in Management Service V1
# NOT from task generation completion in Socket Service V2

# TASK PROGRESS TRACKING REMOVED FROM SOCKET SERVICE V2
# Task progress (attempted_tasks) should ONLY be updated from user task submission in Management Service V1
# NOT from task generation/media generation completion in Socket Service V2