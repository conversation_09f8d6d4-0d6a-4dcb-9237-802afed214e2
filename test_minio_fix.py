#!/usr/bin/env python3
"""
Test script to verify the MinIO client fix in the generate_post endpoint.
"""

import asyncio
import httpx
import json
import os


async def test_generate_post_minio_fix():
    """Test the generate_post endpoint with MinIO fix."""
    
    # Configuration
    BASE_URL = "http://localhost:8000"
    ENDPOINT = "/v1/management/sharepost/generate_post"
    
    # Simple test request
    test_request = {
        "task_set_id": "675e7b8b8b8b8b8b8b8b8b8b",  # Replace with actual task set ID
        "include_audio": False,  # Disable audio for faster testing
        "video_duration_per_image": 2.0,  # Shorter duration
        "max_concurrent_downloads": 3,
        "export_timeout": 60,  # Shorter timeout
        "include_hashtags": True,
        "max_hashtags": 10
    }
    
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers if needed
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"🧪 Testing MinIO fix in generate_post endpoint")
            print(f"Endpoint: {BASE_URL}{ENDPOINT}")
            print(f"Request: {json.dumps(test_request, indent=2)}")
            print()
            
            response = await client.post(
                f"{BASE_URL}{ENDPOINT}",
                json=test_request,
                headers=headers,
                timeout=120.0  # 2 minute timeout
            )
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ MinIO fix successful!")
                print(f"Video URL: {result.get('video_url', 'N/A')}")
                print(f"Caption: {result.get('caption', 'N/A')}")
                print(f"Video metadata: {json.dumps(result.get('video_metadata', {}), indent=2)}")
                
            elif response.status_code == 404:
                print("❌ Task set not found - please update the task_set_id in the test")
                print(f"Response: {response.text}")
                
            elif response.status_code == 401:
                print("❌ Authentication required - please add proper headers")
                print(f"Response: {response.text}")
                
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
                # Check if it's still the MinIO error
                if "create_async_minio_client" in response.text:
                    print("🔍 Still has MinIO client creation error - fix needed")
                else:
                    print("🔍 Different error - MinIO fix may be working")
                
    except Exception as e:
        print(f"❌ Test error: {str(e)}")


def print_test_info():
    """Print test information."""
    print("""
MinIO Fix Test for generate_post Endpoint

This test verifies that the MinIO client creation fix works correctly.

Before running:
1. Make sure your application is running on localhost:8000
2. Update the task_set_id in the test script with a real ID
3. Add authentication headers if required

The fix changes:
- OLD: minio_client = await create_async_minio_client()
- NEW: async_minio_client = create_async_minio_client(current_user.minio)

This should resolve the error:
"create_async_minio_client() missing 1 required positional argument: 'sync_minio_client'"
""")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        print_test_info()
    else:
        print("🔧 Testing MinIO Fix...")
        asyncio.run(test_generate_post_minio_fix())
