#!/usr/bin/env python3
"""
Test script for the combined generate_post endpoint.

This script tests the new endpoint that generates both video and caption in one call.
"""

import asyncio
import httpx
import json
import os
from typing import Dict, Any


async def test_generate_post():
    """Test the combined generate_post endpoint."""
    
    # Configuration
    BASE_URL = "http://localhost:8000"  # Adjust as needed
    ENDPOINT = "/v1/management/sharepost/generate_post"
    
    # Test data - you'll need to replace with actual task_set_id from your database
    test_request = {
        "task_set_id": "YOUR_TASK_SET_ID_HERE",  # Replace with actual task set ID
        "include_audio": True,
        "video_duration_per_image": 3.0,
        "max_concurrent_downloads": 5,
        "export_timeout": 120,
        "include_hashtags": True,
        "max_hashtags": 15
    }
    
    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers here if needed
        # "Authorization": "Bearer YOUR_TOKEN_HERE"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"Testing combined post generation endpoint: {BASE_URL}{ENDPOINT}")
            print(f"Request data: {json.dumps(test_request, indent=2)}")
            
            response = await client.post(
                f"{BASE_URL}{ENDPOINT}",
                json=test_request,
                headers=headers,
                timeout=180.0  # 3 minute timeout for video generation
            )
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Complete post generation successful!")
                print(f"Task Set ID: {result.get('task_set_id', 'N/A')}")
                print(f"Video URL: {result.get('video_url', 'N/A')}")
                print(f"Caption: {result.get('caption', 'N/A')}")
                print(f"Hashtags: {result.get('hashtags', [])}")
                print(f"Full Caption Length: {len(result.get('full_caption', ''))}")
                
                # Video metadata
                video_meta = result.get('video_metadata', {})
                print(f"\nVideo Metadata:")
                print(f"  - Size: {video_meta.get('size_bytes', 0)} bytes")
                print(f"  - Duration: {video_meta.get('duration_seconds', 0)} seconds")
                print(f"  - Story Count: {video_meta.get('story_count', 0)}")
                print(f"  - Include Audio: {video_meta.get('include_audio', False)}")
                
                # Caption metadata
                caption_meta = result.get('caption_metadata', {})
                print(f"\nCaption Metadata:")
                print(f"  - Task Set Title: {caption_meta.get('task_set_title', 'N/A')}")
                print(f"  - Story Count: {caption_meta.get('story_count', 0)}")
                print(f"  - Task Count: {caption_meta.get('task_count', 0)}")
                print(f"  - Character Count: {caption_meta.get('character_count', 0)}")
                
                print(f"\n📱 Ready for Social Media!")
                print(f"Video: {result.get('video_url', 'N/A')}")
                print(f"Caption: {result.get('full_caption', 'N/A')}")
                
            else:
                print(f"❌ Post generation failed!")
                print(f"Error response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing post generation: {str(e)}")


def print_usage():
    """Print usage instructions."""
    print("""
Combined Post Generation Test Script

Before running this test:

1. Make sure your application is running on localhost:8000
2. Replace 'YOUR_TASK_SET_ID_HERE' with an actual task set ID from your database
3. Add proper authentication headers if required
4. Ensure the task set has stories with images for video generation

To test the endpoint:
    python test_generate_post.py

Example task set IDs you can try:
- Check your database for existing task sets with stories
- Use the management service to list task sets
- Create a new task set with stories through the application

The endpoint will:
1. Generate a slideshow video from task set stories
2. Create a simple caption using the task title
3. Add predefined hashtags
4. Return both video and caption ready for social media

This combines both:
- /v1/management/sharepost/generate_video
- /v1/management/sharepost/captions/create_caption

Into a single convenient endpoint!
""")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        print_usage()
    else:
        print("🧪 Testing Combined Post Generation...")
        asyncio.run(test_generate_post())
