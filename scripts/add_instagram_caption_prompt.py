#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add Instagram caption generation prompt to the database.

This script adds the necessary prompt template for Instagram caption generation
to the prompts collection in the database.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.shared.database import DatabaseManager
from app.shared.config import Config


async def add_instagram_caption_prompt():
    """Add Instagram caption generation prompt to the database."""
    
    # Instagram caption prompt template
    instagram_caption_prompt = {
        "name": "instagram_caption_prompt",
        "prompt": """You are a social media expert creating engaging Instagram captions for educational content.

Create an Instagram-ready caption for this Nepali learning content:

CONTENT SUMMARY:
- Task Set Title: {title}
- Number of Stories: {story_count}
- Number of Tasks: {task_count}
- Content Type: {content_type}
- Stories: {stories_summary}
- Tasks: {tasks_summary}

CAPTION REQUIREMENTS:
- Style: {caption_style}
- Language: {language} (use Nepali for main content, English for hashtags if mixed)
- Make it engaging and shareable
- Include emojis appropriately
- Keep it under 2200 characters
- Focus on learning benefits and cultural value
- Encourage interaction (ask questions, use call-to-action)
- Highlight the educational value and fun aspects

HASHTAG REQUIREMENTS:
- Generate exactly {max_hashtags} relevant hashtags
- Mix of Nepali learning, education, culture, and trending tags
- Include #NepaliLanguage #LearnNepali #Education
- Use English for hashtags for better reach
- Include relevant educational hashtags like #LanguageLearning #KidsEducation
- Add cultural hashtags like #NepalCulture #SouthAsian
- Include engagement hashtags like #Interactive #Fun

CAPTION STYLE GUIDELINES:
- engaging: Use exciting language, emojis, and call-to-action
- educational: Focus on learning benefits and knowledge sharing
- fun: Use playful language and emphasize enjoyment
- motivational: Inspire learners and highlight achievements

Return your response in this exact JSON format:
{{
  "caption": "Main caption text here",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3", ...]
}}""",
        "description": "Template for generating Instagram captions for Nepali learning content",
        "category": "social_media",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
        "version": "1.0",
        "metadata": {
            "supported_styles": ["engaging", "educational", "fun", "motivational"],
            "supported_languages": ["nepali", "english", "mixed"],
            "max_hashtags_range": [5, 30],
            "character_limit": 2200
        }
    }
    
    try:
        # Initialize database manager
        config = Config()
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        # Get admin database
        admin_db = db_manager.get_admin_database()
        
        # Check if prompt already exists
        existing_prompt = await admin_db.prompts.find_one({"name": "instagram_caption_prompt"})
        
        if existing_prompt:
            print("Instagram caption prompt already exists. Updating...")
            # Update existing prompt
            result = await admin_db.prompts.update_one(
                {"name": "instagram_caption_prompt"},
                {"$set": {
                    "prompt": instagram_caption_prompt["prompt"],
                    "description": instagram_caption_prompt["description"],
                    "updated_at": datetime.now(timezone.utc),
                    "version": instagram_caption_prompt["version"],
                    "metadata": instagram_caption_prompt["metadata"]
                }}
            )
            print(f"Updated Instagram caption prompt. Modified count: {result.modified_count}")
        else:
            print("Adding new Instagram caption prompt...")
            # Insert new prompt
            result = await admin_db.prompts.insert_one(instagram_caption_prompt)
            print(f"Added Instagram caption prompt with ID: {result.inserted_id}")
        
        print("✅ Instagram caption prompt successfully added/updated in database!")
        
    except Exception as e:
        print(f"❌ Error adding Instagram caption prompt: {str(e)}")
        raise
    finally:
        # Close database connection
        if 'db_manager' in locals():
            await db_manager.close()


if __name__ == "__main__":
    print("Adding Instagram caption prompt to database...")
    asyncio.run(add_instagram_caption_prompt())
