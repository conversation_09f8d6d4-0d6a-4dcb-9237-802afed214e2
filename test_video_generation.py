#!/usr/bin/env python3
"""
Test script for video generation functionality.

This script tests the video generation endpoint by:
1. Creating mock data for testing
2. Testing the video generation API endpoint
3. Verifying the response and generated video

Usage:
    python test_video_generation.py
"""

import asyncio
import httpx
import json
import os
from datetime import datetime, timezone
from bson import ObjectId

# Test configuration
BASE_URL = "http://localhost:8000/v1/management"
TEST_TASK_SET_ID = "60f1b2b3c4d5e6f7a8b9c0d1"  # Mock task set ID

async def test_video_generation():
    """Test the video generation endpoint."""
    
    print("🎬 Testing Video Generation Endpoint")
    print("=" * 50)
    
    # Test data for video generation
    test_request = {
        "task_set_id": TEST_TASK_SET_ID,
        "include_audio": True,
        "video_duration_per_image": 3.0
    }
    
    print(f"📋 Test Request:")
    print(json.dumps(test_request, indent=2))
    print()
    
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:
            # Test the video generation endpoint
            print("🚀 Sending request to video generation endpoint...")
            
            response = await client.post(
                f"{BASE_URL}/sharepost/generate_video",
                json=test_request,
                headers={
                    "Content-Type": "application/json",
                    # Note: In real usage, you'd need proper authentication headers
                    # "Authorization": "Bearer your_jwt_token_here"
                }
            )
            
            print(f"📊 Response Status: {response.status_code}")
            print(f"📊 Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Video generation successful!")
                print(f"📹 Video URL: {result.get('video_url')}")
                print(f"📊 Processing Status: {result.get('processing_status')}")
                print(f"📋 Metadata:")
                print(json.dumps(result.get('video_metadata', {}), indent=2, default=str))
                
            elif response.status_code == 404:
                print("❌ Task set not found - this is expected for mock data")
                print(f"Response: {response.text}")
                
            elif response.status_code == 401:
                print("🔐 Authentication required - this is expected without proper JWT token")
                print(f"Response: {response.text}")
                
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                print(f"Response: {response.text}")
                
    except httpx.ConnectError:
        print("❌ Connection failed - make sure the management service is running")
        print("   Try: uvicorn app.v1.api.management_service:app --host 0.0.0.0 --port 8000")
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")


async def test_preview_endpoint():
    """Test the task set preview endpoint."""
    
    print("\n🔍 Testing Task Set Preview Endpoint")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/sharepost/task_set/{TEST_TASK_SET_ID}/preview",
                headers={
                    "Content-Type": "application/json",
                    # Note: In real usage, you'd need proper authentication headers
                }
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Preview successful!")
                print(f"📋 Preview Data:")
                print(json.dumps(result, indent=2, default=str))
                
            elif response.status_code == 404:
                print("❌ Task set not found - this is expected for mock data")
                print(f"Response: {response.text}")
                
            elif response.status_code == 401:
                print("🔐 Authentication required - this is expected without proper JWT token")
                print(f"Response: {response.text}")
                
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error during preview test: {str(e)}")


async def test_status_endpoint():
    """Test the video status endpoint."""
    
    print("\n📊 Testing Video Status Endpoint")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{BASE_URL}/sharepost/video_status/{TEST_TASK_SET_ID}",
                headers={
                    "Content-Type": "application/json",
                    # Note: In real usage, you'd need proper authentication headers
                }
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Status check successful!")
                print(f"📋 Video Status:")
                print(json.dumps(result, indent=2, default=str))
                
            elif response.status_code == 401:
                print("🔐 Authentication required - this is expected without proper JWT token")
                print(f"Response: {response.text}")
                
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error during status test: {str(e)}")


def print_usage_instructions():
    """Print usage instructions for the video generation API."""
    
    print("\n📖 Video Generation API Usage")
    print("=" * 50)
    print("""
🎯 Endpoints Available:

1. Generate Video:
   POST /v1/management/sharepost/generate_video
   Body: {
     "task_set_id": "your_task_set_id",
     "include_audio": true,
     "video_duration_per_image": 3.0
   }

2. Preview Task Set Media:
   GET /v1/management/sharepost/task_set/{task_set_id}/preview

3. Check Video Status:
   GET /v1/management/sharepost/video_status/{task_set_id}

🔐 Authentication:
   All endpoints require proper JWT authentication.
   Include: Authorization: Bearer <your_jwt_token>

📋 Requirements:
   - Task set must exist in the database
   - Stories in the task set must have image_metadata.url
   - Audio is optional (controlled by include_audio parameter)

🎬 Video Generation Process:
   1. Fetches task set and associated stories
   2. Downloads images and audio from MinIO URLs
   3. Creates slideshow video using MoviePy
   4. Uploads generated video to MinIO
   5. Returns video URL and metadata

📊 Response Format:
   {
     "video_url": "https://minio.../video.mp4",
     "video_metadata": { ... },
     "processing_status": "completed"
   }
""")


async def main():
    """Main test function."""
    
    print("🎬 Video Generation API Test Suite")
    print("=" * 50)
    print(f"🕒 Test started at: {datetime.now(timezone.utc).isoformat()}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"🆔 Test Task Set ID: {TEST_TASK_SET_ID}")
    print()
    
    # Run all tests
    await test_preview_endpoint()
    await test_status_endpoint()
    await test_video_generation()
    
    # Print usage instructions
    print_usage_instructions()
    
    print(f"\n🕒 Test completed at: {datetime.now(timezone.utc).isoformat()}")
    print("✅ Test suite finished!")


if __name__ == "__main__":
    asyncio.run(main())
